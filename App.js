import React from 'react';
import { View, Text } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/context/AuthContext';
import { UserProvider } from './src/context/UserContext';
import AppNavigator from './src/navigation/AppNavigator';
import { maclarenLightTheme } from './src/theme/theme';

// Componente de prueba simple
const TestApp = () => {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#333' }}>🚗 Maclaren</Text>
      <Text style={{ fontSize: 16, color: '#666', marginTop: 10 }}>Aplicación funcionando correctamente</Text>
    </View>
  );
};

export default function App() {
  // Temporalmente usar componente de prueba
  const useTestMode = false;

  if (useTestMode) {
    return <TestApp />;
  }

  return (
    <SafeAreaProvider>
      <PaperProvider theme={maclarenLightTheme}>
        <AuthProvider>
          <UserProvider>
            <NavigationContainer>
              <AppNavigator />
              <StatusBar style="light" backgroundColor="#1a1a2e" />
            </NavigationContainer>
          </UserProvider>
        </AuthProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
