# 📋 Características Técnicas - Maclaren

## 🏗️ Arquitectura del Sistema

### Frontend (React Native + Expo)
- **Framework**: React Native con Expo SDK
- **Navegación**: React Navigation v6
- **UI Components**: React Native Paper (Material Design)
- **Mapas**: React Native Maps + Google Maps
- **Estado Global**: React Context API
- **Almacenamiento**: AsyncStorage
- **HTTP Client**: Axios
- **Permisos**: Expo Location, Camera, ImagePicker

### Backend (Node.js + Express)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Base de Datos**: MongoDB con Mongoose ODM
- **Autenticación**: JWT (JSON Web Tokens)
- **Tiempo Real**: Socket.IO
- **Archivos**: Multer para uploads
- **Seguridad**: bcryptjs para passwords
- **CORS**: Habilitado para desarrollo

## 🔐 Sistema de Autenticación

### Registro de Usuario
- Validación de campos obligatorios
- Encriptación de contraseñas con bcrypt
- Verificación de email único
- Subida de documentos requeridos

### Verificación de Documentos
**Pasajeros:**
- Cédula de ciudadanía
- Recibo de servicios públicos

**Conductores (adicional):**
- Registro vehicular
- SOAT vigente
- Licencia de conducir
- Foto de placa del vehículo

### Autorización
- Middleware JWT para rutas protegidas
- Roles diferenciados (pasajero/conductor)
- Verificación de permisos por endpoint

## 🗄️ Modelos de Base de Datos

### Usuario (User)
```javascript
{
  firstName: String,
  lastName: String,
  email: String (único),
  password: String (encriptado),
  phone: String,
  address: String,
  userType: ['passenger', 'driver', 'both'],
  documents: [DocumentSchema],
  documentsVerified: Boolean,
  balance: Number,
  driverInfo: {
    vehicleType: String,
    vehiclePlate: String,
    isAvailable: Boolean,
    currentLocation: {
      latitude: Number,
      longitude: Number
    }
  },
  stats: {
    totalTrips: Number,
    completedTrips: Number,
    totalEarnings: Number
  }
}
```

### Viaje (Trip)
```javascript
{
  passenger: ObjectId (ref: User),
  driver: ObjectId (ref: User),
  pickupLocation: {
    latitude: Number,
    longitude: Number,
    address: String
  },
  destination: {
    latitude: Number,
    longitude: Number,
    address: String
  },
  status: ['pending', 'accepted', 'in_progress', 'completed', 'cancelled'],
  offeredPrice: Number,
  finalPrice: Number,
  commission: Number,
  timeline: [TimelineEvent],
  driverRoute: [LocationPoint]
}
```

### Transacción (Transaction)
```javascript
{
  user: ObjectId (ref: User),
  type: ['recharge', 'commission', 'payment'],
  amount: Number,
  description: String,
  status: ['pending', 'completed', 'failed'],
  balanceBefore: Number,
  balanceAfter: Number,
  trip: ObjectId (ref: Trip)
}
```

## 🔄 Flujos de Negocio

### Solicitud de Viaje (Pasajero)
1. Usuario selecciona origen y destino en mapa
2. Ingresa precio ofrecido
3. Sistema crea viaje con status 'pending'
4. Socket.IO notifica a conductores cercanos
5. Viaje aparece en mapa para conductores

### Aceptación de Viaje (Conductor)
1. Conductor ve viajes disponibles en mapa
2. Sistema verifica saldo mínimo ($10)
3. Conductor acepta viaje
4. Status cambia a 'accepted'
5. Socket.IO notifica al pasajero

### Ejecución de Viaje
1. Conductor inicia viaje (status: 'in_progress')
2. Ubicación del conductor se actualiza en tiempo real
3. Pasajero ve progreso en mapa
4. Conductor completa viaje
5. Sistema procesa pago y comisión (10%)

### Sistema de Pagos
1. Conductores mantienen saldo prepago
2. Recarga mínima: $10
3. Comisión automática: 10% por viaje
4. Historial completo de transacciones
5. Bloqueo automático si saldo < $10

## 🌐 API Endpoints

### Autenticación
- `POST /api/auth/register` - Registro
- `POST /api/auth/login` - Login
- `POST /api/auth/upload-documents` - Subir documentos
- `GET /api/auth/me` - Perfil actual

### Viajes
- `POST /api/trips/request` - Solicitar viaje
- `GET /api/trips/available` - Viajes disponibles
- `POST /api/trips/:id/accept` - Aceptar viaje
- `POST /api/trips/:id/start` - Iniciar viaje
- `POST /api/trips/:id/complete` - Completar viaje
- `POST /api/trips/:id/cancel` - Cancelar viaje
- `GET /api/trips/history/:userId` - Historial

### Pagos
- `POST /api/payments/recharge` - Recargar saldo
- `GET /api/payments/balance/:userId` - Consultar saldo
- `GET /api/payments/history/:userId` - Historial transacciones

### Usuarios
- `GET /api/users/profile` - Obtener perfil
- `PUT /api/users/profile` - Actualizar perfil
- `PUT /api/users/driver-availability` - Cambiar disponibilidad

## 🔌 Comunicación en Tiempo Real (Socket.IO)

### Eventos del Cliente
- `join-trip` - Unirse a sala de viaje
- `update-driver-location` - Actualizar ubicación
- `new-trip-request` - Nuevo viaje solicitado

### Eventos del Servidor
- `trip-available` - Viaje disponible para conductores
- `trip-accepted` - Viaje aceptado
- `driver-location-updated` - Ubicación actualizada
- `trip-completed` - Viaje completado

## 📱 Funcionalidades Móviles

### Geolocalización
- Obtención de ubicación actual
- Seguimiento en tiempo real
- Cálculo de rutas
- Mapas interactivos

### Cámara y Galería
- Captura de documentos
- Selección desde galería
- Validación de tipos de archivo
- Compresión automática

### Navegación
- Stack Navigator para autenticación
- Tab Navigator para funciones principales
- Navegación condicional según rol

### Almacenamiento Local
- Tokens de autenticación
- Preferencias de usuario
- Cache de datos temporales

## 🔒 Seguridad Implementada

### Backend
- Validación de entrada en todos los endpoints
- Sanitización de datos
- Rate limiting (pendiente)
- HTTPS en producción (pendiente)
- Validación de tipos de archivo

### Frontend
- Validación de formularios
- Manejo seguro de tokens
- Verificación de permisos
- Sanitización de entrada de usuario

## 📊 Métricas y Monitoreo

### Logs del Sistema
- Conexiones de base de datos
- Errores de API
- Eventos de Socket.IO
- Transacciones de pago

### Estadísticas de Usuario
- Total de viajes
- Viajes completados/cancelados
- Ganancias totales
- Calificaciones promedio

## 🚀 Optimizaciones

### Performance
- Índices de base de datos optimizados
- Paginación en listados
- Cache de consultas frecuentes
- Compresión de imágenes

### UX/UI
- Loading states
- Error handling
- Feedback visual
- Navegación intuitiva

## 🔮 Extensibilidad

### Integraciones Futuras
- Gateway de pagos real (Stripe/PayPal)
- Notificaciones push (FCM)
- Análisis avanzado (Analytics)
- Chat en tiempo real
- Sistema de calificaciones
- Soporte multi-idioma

### Escalabilidad
- Arquitectura modular
- Separación de responsabilidades
- APIs RESTful estándar
- Base de datos NoSQL escalable
