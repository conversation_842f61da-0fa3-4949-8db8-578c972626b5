# 📱 Cómo Ejecutar Maclaren en Expo Go

## 🚀 Pasos para Abrir la App

### 1. Abrir Terminal en el Proyecto
```bash
cd C:\Users\<USER>\Desktop\maclaren
```

### 2. Instalar Dependencias (si no se ha hecho)
```bash
npm install
```

### 3. Iniciar Expo
```bash
npm start
```
O alternativamente:
```bash
npx expo start
```

### 4. Opciones de Visualización

#### Opción A: Expo Go en tu Teléfono (Recomendado)
1. **Instala Expo Go** desde:
   - [Google Play Store](https://play.google.com/store/apps/details?id=host.exp.exponent) (Android)
   - [App Store](https://apps.apple.com/app/expo-go/id982107779) (iOS)

2. **Escanea el QR Code** que aparece en la terminal

3. **La app se abrirá automáticamente** en Expo Go

#### Opción B: Navegador Web
1. Presiona `w` en la terminal para abrir en navegador
2. O ve directamente a: http://localhost:19006

#### Opción C: Emulador Android
1. Asegúrate de tener Android Studio instalado
2. Inicia un emulador Android
3. Presiona `a` en la terminal de Expo

#### Opción D: Simulador iOS (solo macOS)
1. Asegúrate de tener Xcode instalado
2. Presiona `i` en la terminal de Expo

## 🎯 Lo que Verás

Actualmente la app está configurada en modo de prueba y mostrará:
- 🏍️ **Maclaren** (título)
- **Plataforma de Moto-Taxi** (subtítulo)
- **✅ App funcionando correctamente** (estado)

## 🔄 Para Activar la App Completa

Una vez que confirmes que la app básica funciona, puedes activar todas las funcionalidades:

1. **Restaurar App.js completo**:
   - Descomenta el código completo en `App.js`
   - O reemplaza con la versión completa

2. **Configurar Google Maps**:
   - Obtén API Key de Google Cloud Console
   - Reemplaza `YOUR_GOOGLE_MAPS_API_KEY` en `app.json`

3. **Iniciar Backend**:
   ```bash
   cd backend
   npm install
   npm run dev
   ```

## 🧪 Usuarios de Prueba

Una vez que la app completa esté funcionando:
- **Email**: `<EMAIL>`
- **Password**: `123456`
- **Tipo**: Conductor (con saldo $25)

## 🔧 Solución de Problemas

### Si no aparece el QR Code:
```bash
npx expo start --tunnel
```

### Si hay errores de dependencias:
```bash
npm install --legacy-peer-deps
```

### Para limpiar cache:
```bash
npx expo start --clear
```

### Si Metro bundler falla:
```bash
npx expo start --reset-cache
```

## 📱 Comandos Útiles en la Terminal de Expo

Una vez que Expo esté ejecutándose, puedes usar:
- `w` - Abrir en navegador web
- `a` - Abrir en emulador Android
- `i` - Abrir en simulador iOS
- `r` - Recargar app
- `m` - Alternar menú de desarrollo
- `j` - Abrir debugger
- `c` - Limpiar cache y recargar

## ✅ Verificación de Funcionamiento

La app está funcionando correctamente si:
1. ✅ Expo inicia sin errores
2. ✅ Aparece QR code en terminal
3. ✅ App se abre en Expo Go
4. ✅ Ves la pantalla de "Maclaren"

## 🆘 Si Necesitas Ayuda

1. **Revisa los logs** en la terminal donde ejecutaste `npm start`
2. **Verifica que tu teléfono y PC estén en la misma red WiFi**
3. **Asegúrate de tener la última versión de Expo Go**
4. **Intenta con `npx expo start --tunnel` si hay problemas de red**

---

**¡Una vez que veas la pantalla de Maclaren, la app está lista! 🎉**
