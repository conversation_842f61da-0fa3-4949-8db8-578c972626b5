# 🚀 Instrucciones de Ejecución - Maclaren

## ⚡ Inicio <PERSON> (5 minutos)

### 1. Preparar MongoDB
```bash
# Opción A: MongoDB Local (si tienes MongoDB instalado)
mongod

# Opción B: Usar MongoDB Atlas (recomendado)
# 1. Ve a https://www.mongodb.com/atlas
# 2. Crea cuenta gratuita
# 3. Crea cluster
# 4. Obtén cadena de conexión
# 5. Edita backend/.env y reemplaza MONGODB_URI
```

### 2. Configurar Google Maps (IMPORTANTE)
```bash
# 1. Ve a https://console.cloud.google.com/
# 2. Crea proyecto
# 3. Habilita APIs: Maps SDK for Android, Maps SDK for iOS
# 4. Crea API Key
# 5. Edita app.json y reemplaza YOUR_GOOGLE_MAPS_API_KEY
```

### 3. Ejecutar Backend
```bash
# Terminal 1: Backend
cd backend
npm install
npm run seed    # Crear usuarios de prueba
npm run dev     # Iniciar servidor
```

### 4. Ejecutar Frontend
```bash
# Terminal 2: Frontend (nueva terminal)
npm install
npm start       # Iniciar Expo
```

### 5. Abrir en Dispositivo
- **Opción A**: Instala "Expo Go" en tu teléfono y escanea el QR
- **Opción B**: Presiona 'a' para Android emulator
- **Opción C**: Presiona 'i' para iOS simulator (solo macOS)

## 🧪 Usuarios de Prueba

Después de ejecutar `npm run seed`:

| Email | Password | Tipo | Saldo |
|-------|----------|------|-------|
| <EMAIL> | 123456 | Conductor | $25.00 |
| <EMAIL> | 123456 | Pasajero | $0.00 |

## 🎯 Flujo de Prueba

### Como Pasajero:
1. Login: `<EMAIL>` / `123456`
2. Permitir ubicación
3. Tocar en mapa para destino
4. Ingresar precio: `$8.50`
5. Confirmar solicitud

### Como Conductor:
1. Login: `<EMAIL>` / `123456`
2. Cambiar a modo "Conductor"
3. Ver solicitudes en mapa
4. Aceptar viaje
5. Completar viaje

## 🔧 Solución de Problemas

### "Metro bundler failed"
```bash
npx expo start --clear
```

### "Unable to resolve module"
```bash
rm -rf node_modules
npm install
```

### Error MongoDB
- Verificar que MongoDB esté ejecutándose
- Revisar MONGODB_URI en backend/.env

### Error Google Maps
- Verificar API Key en app.json
- Habilitar APIs necesarias en Google Cloud

## 📱 URLs Importantes

- **Backend API**: http://localhost:3000/api
- **Health Check**: http://localhost:3000/api/health
- **Documentos**: http://localhost:3000/uploads/documents/

## 🎉 ¡Listo!

Si todo funciona correctamente, verás:
- ✅ Servidor backend en puerto 3000
- ✅ App móvil en Expo Go
- ✅ Mapas funcionando
- ✅ Login exitoso
- ✅ Cambio entre pasajero/conductor

## 📞 Soporte

Si tienes problemas:
1. Revisa los logs en ambas terminales
2. Verifica configuraciones en .env y app.json
3. Consulta SETUP.md para guía detallada
