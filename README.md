# 🏍️ Maclaren - Plataforma de Moto-Taxi

Maclaren es una aplicación móvil completa para servicios de moto-taxi que permite a los usuarios cambiar entre modo pasajero y conductor en la misma aplicación. Los pasajeros pueden ofrecer precios por sus viajes y los conductores pueden aceptar las ofertas que les convengan.

## ✨ Características Principales

### 🔄 Funcionalidades Generales
- **Cambio de Rol**: Los usuarios pueden alternar entre modo pasajero y conductor
- **Autenticación Completa**: Registro, login y verificación de documentos
- **Mapas Integrados**: Visualización en tiempo real con Google Maps
- **Sistema de Pagos**: Recarga de saldo y gestión de comisiones (10%)
- **Historial de Viajes**: Seguimiento completo de todas las transacciones

### 👤 Modo Pasajero
- Solicitar viajes ofreciendo un precio
- Seleccionar origen y destino en el mapa
- Seguimiento en tiempo real del conductor
- Historial de viajes realizados

### 🏍️ Modo Conductor
- Ver solicitudes de viajes disponibles
- Aceptar viajes según conveniencia
- Sistema de saldo prepago ($10 mínimo)
- Comisión del 10% por viaje completado
- Seguimiento de ganancias y estadísticas

### 📋 Verificación de Documentos
**Para Pasajeros:**
- Cédula de ciudadanía
- Recibo de servicios públicos (comprobante de domicilio)

**Para Conductores (adicional):**
- Registro vehicular
- SOAT vigente
- Licencia de conducir
- Foto de la placa del vehículo

## 🛠️ Tecnologías Utilizadas

### Frontend (React Native + Expo)
- **React Native**: Framework principal
- **Expo**: Plataforma de desarrollo
- **React Navigation**: Navegación entre pantallas
- **React Native Paper**: Componentes UI
- **React Native Maps**: Integración de mapas
- **Expo Location**: Geolocalización
- **Expo Image Picker**: Captura y selección de imágenes
- **AsyncStorage**: Almacenamiento local
- **Axios**: Cliente HTTP

### Backend (Node.js + Express)
- **Node.js**: Runtime de JavaScript
- **Express**: Framework web
- **MongoDB**: Base de datos NoSQL
- **Mongoose**: ODM para MongoDB
- **Socket.IO**: Comunicación en tiempo real
- **JWT**: Autenticación con tokens
- **Multer**: Manejo de archivos
- **bcryptjs**: Encriptación de contraseñas

## 📱 Instalación y Configuración

### Prerrequisitos
- Node.js (v14 o superior)
- MongoDB (local o MongoDB Atlas)
- Expo CLI
- Android Studio o Xcode (para emuladores)

### 1. Clonar el Repositorio
```bash
git clone <repository-url>
cd maclaren
```

### 2. Configurar el Backend
```bash
cd backend
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Iniciar el servidor
npm run dev
```

### 3. Configurar el Frontend
```bash
# Desde la raíz del proyecto
npm install

# Configurar Google Maps API Key en app.json
# Reemplazar YOUR_GOOGLE_MAPS_API_KEY con tu clave real

# Iniciar la aplicación
npm start
```

### 4. Configurar MongoDB
Asegúrate de tener MongoDB ejecutándose localmente o configura MongoDB Atlas:

```bash
# Para MongoDB local
mongod

# O configura MONGODB_URI en backend/.env para MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/maclaren
```

## 🔧 Configuración de Google Maps

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita las siguientes APIs:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
   - Directions API
4. Crea credenciales (API Key)
5. Reemplaza `YOUR_GOOGLE_MAPS_API_KEY` en `app.json`

## 🚀 Uso de la Aplicación

### Registro de Usuario
1. Abre la aplicación
2. Selecciona "Registrarse"
3. Completa la información personal
4. Sube los documentos requeridos
5. Espera la verificación

### Como Pasajero
1. Cambia a modo "Pasajero"
2. Selecciona tu ubicación actual
3. Toca en el mapa para seleccionar destino
4. Ingresa el precio que ofreces
5. Confirma la solicitud
6. Espera a que un conductor acepte

### Como Conductor
1. Asegúrate de tener saldo mínimo ($10)
2. Cambia a modo "Conductor"
3. Ve los viajes disponibles en el mapa
4. Toca un marcador para ver detalles
5. Acepta el viaje que te convenga
6. Completa el viaje para recibir el pago

## 💰 Sistema de Pagos

### Recarga de Saldo
- Los conductores deben mantener un saldo mínimo de $10
- Recarga disponible de $10 por transacción
- Sistema de pago simulado (integración real pendiente)

### Comisiones
- 10% de comisión por cada viaje completado
- Descuento automático del saldo del conductor
- Historial completo de transacciones

## 📊 Estructura del Proyecto

```
maclaren/
├── src/                    # Código fuente React Native
│   ├── components/         # Componentes reutilizables
│   ├── screens/           # Pantallas de la aplicación
│   ├── navigation/        # Configuración de navegación
│   ├── context/           # Context API (estado global)
│   ├── services/          # Servicios API
│   └── utils/             # Utilidades
├── backend/               # Servidor Node.js
│   ├── models/            # Modelos de MongoDB
│   ├── routes/            # Rutas de la API
│   ├── middleware/        # Middleware personalizado
│   ├── config/            # Configuraciones
│   └── uploads/           # Archivos subidos
├── assets/                # Recursos estáticos
└── docs/                  # Documentación
```

## 🔒 Seguridad

- Autenticación JWT
- Encriptación de contraseñas con bcrypt
- Validación de archivos subidos
- Verificación de documentos obligatoria
- Middleware de autorización por roles

## 🧪 Testing

```bash
# Backend tests
cd backend
npm test

# Frontend tests
npm test
```

## 📝 API Endpoints

### Autenticación
- `POST /api/auth/register` - Registro de usuario
- `POST /api/auth/login` - Inicio de sesión
- `POST /api/auth/upload-documents` - Subir documentos

### Viajes
- `POST /api/trips/request` - Solicitar viaje
- `GET /api/trips/available` - Viajes disponibles
- `POST /api/trips/:id/accept` - Aceptar viaje
- `POST /api/trips/:id/complete` - Completar viaje

### Pagos
- `POST /api/payments/recharge` - Recargar saldo
- `GET /api/payments/balance/:userId` - Consultar saldo
- `GET /api/payments/history/:userId` - Historial de transacciones

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 📞 Soporte

Para soporte técnico o preguntas:
- Email: <EMAIL>
- Issues: [GitHub Issues](https://github.com/your-repo/maclaren/issues)

## 🔮 Roadmap

- [ ] Integración con gateway de pagos real (Stripe/PayPal)
- [ ] Notificaciones push
- [ ] Sistema de calificaciones
- [ ] Chat en tiempo real
- [ ] Modo offline
- [ ] Análisis y reportes avanzados
- [ ] Soporte multi-idioma

---

**Desarrollado con ❤️ para la comunidad de transporte urbano**
#   M A C L A R E N 
 
 