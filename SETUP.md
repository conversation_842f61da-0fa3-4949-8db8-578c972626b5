# 🚀 Guía de Configuración Rápida - Maclaren

Esta guía te ayudará a configurar y ejecutar la aplicación Maclaren en tu entorno de desarrollo.

## 📋 Prerrequisitos

Antes de comenzar, asegúrate de tener instalado:

- **Node.js** (v14 o superior) - [<PERSON><PERSON><PERSON>](https://nodejs.org/)
- **MongoDB** - [<PERSON><PERSON><PERSON>](https://www.mongodb.com/try/download/community) o usar [MongoDB Atlas](https://www.mongodb.com/atlas)
- **Expo CLI** - `npm install -g @expo/cli`
- **Git** - [<PERSON><PERSON><PERSON>](https://git-scm.com/)

### Para desarrollo móvil:
- **Android Studio** (para emulador Android)
- **Xcode** (para emulador iOS - solo macOS)
- **Expo Go** app en tu teléfono móvil

## 🛠️ Configuración Paso a Paso

### 1. Configurar MongoDB

#### Opción A: MongoDB Local
```bash
# Instalar MongoDB y ejecutar
mongod
```

#### Opción B: MongoDB Atlas (Recomendado)
1. Ve a [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Crea una cuenta gratuita
3. Crea un cluster
4. Obtén la cadena de conexión
5. Reemplaza en `backend/.env`: `MONGODB_URI=tu_cadena_de_conexion`

### 2. Configurar Google Maps API

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un proyecto nuevo
3. Habilita estas APIs:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
   - Directions API
4. Crea una API Key
5. Reemplaza en `app.json`: `YOUR_GOOGLE_MAPS_API_KEY`

### 3. Configurar el Backend

```bash
# Navegar al directorio backend
cd backend

# Instalar dependencias
npm install

# Configurar variables de entorno
# Edita el archivo .env con tus configuraciones
# MONGODB_URI, JWT_SECRET, etc.

# Poblar base de datos con datos de prueba
npm run seed

# Iniciar servidor en modo desarrollo
npm run dev
```

El servidor estará disponible en: `http://localhost:3000`

### 4. Configurar el Frontend

```bash
# Desde la raíz del proyecto
npm install

# Iniciar la aplicación
npm start
```

### 5. Ejecutar en Dispositivo/Emulador

#### Opción A: Expo Go (Más fácil)
1. Instala Expo Go en tu teléfono
2. Escanea el QR code que aparece en la terminal
3. La app se abrirá en Expo Go

#### Opción B: Emulador Android
```bash
# Asegúrate de tener Android Studio instalado
npm run android
```

#### Opción C: Emulador iOS (solo macOS)
```bash
# Asegúrate de tener Xcode instalado
npm run ios
```

## 🧪 Datos de Prueba

Después de ejecutar `npm run seed`, tendrás estos usuarios disponibles:

| Email | Password | Tipo | Saldo |
|-------|----------|------|-------|
| <EMAIL> | 123456 | Conductor/Pasajero | $25.00 |
| <EMAIL> | 123456 | Pasajero | $0.00 |
| <EMAIL> | 123456 | Conductor | $15.50 |
| <EMAIL> | 123456 | Pasajero | $0.00 |

## 🔧 Solución de Problemas Comunes

### Error: "Metro bundler failed to start"
```bash
# Limpiar cache de Expo
npx expo start --clear
```

### Error: "Unable to resolve module"
```bash
# Reinstalar dependencias
rm -rf node_modules
npm install
```

### Error de conexión a MongoDB
- Verifica que MongoDB esté ejecutándose
- Revisa la cadena de conexión en `.env`
- Asegúrate de que tu IP esté en la whitelist (MongoDB Atlas)

### Error de Google Maps
- Verifica que la API Key esté configurada correctamente
- Asegúrate de que las APIs necesarias estén habilitadas
- Revisa las restricciones de la API Key

### Error de permisos de ubicación
- En Android: Ve a Configuración > Apps > Expo Go > Permisos
- En iOS: Ve a Configuración > Privacidad > Servicios de ubicación

## 📱 Flujo de Prueba Recomendado

### Como Pasajero:
1. Inicia sesión con `<EMAIL>`
2. Permite permisos de ubicación
3. Toca en el mapa para seleccionar destino
4. Ingresa un precio (ej: $8.50)
5. Confirma la solicitud

### Como Conductor:
1. Inicia sesión con `<EMAIL>`
2. Cambia a modo "Conductor"
3. Verifica que tengas saldo suficiente
4. Ve las solicitudes disponibles en el mapa
5. Acepta un viaje
6. Completa el viaje

## 🔄 Comandos Útiles

```bash
# Backend
cd backend
npm run dev          # Servidor en modo desarrollo
npm run seed         # Poblar base de datos
npm start           # Servidor en modo producción

# Frontend
npm start           # Iniciar Expo
npm run android     # Ejecutar en Android
npm run ios         # Ejecutar en iOS
npm run web         # Ejecutar en navegador
```

## 📊 Monitoreo

### Logs del Backend
Los logs del servidor aparecerán en la terminal donde ejecutaste `npm run dev`

### Logs del Frontend
Los logs de la app aparecerán en:
- Terminal de Expo
- Consola del navegador (si usas web)
- Herramientas de desarrollo de Expo

## 🆘 Obtener Ayuda

Si encuentras problemas:

1. **Revisa los logs** en ambas terminales (backend y frontend)
2. **Verifica las configuraciones** en `.env` y `app.json`
3. **Consulta la documentación** de las tecnologías utilizadas
4. **Crea un issue** en el repositorio con detalles del error

## ✅ Checklist de Configuración

- [ ] Node.js instalado
- [ ] MongoDB configurado y ejecutándose
- [ ] Google Maps API Key configurada
- [ ] Backend ejecutándose en puerto 3000
- [ ] Base de datos poblada con datos de prueba
- [ ] Frontend iniciado con Expo
- [ ] Permisos de ubicación otorgados
- [ ] Aplicación funcionando en dispositivo/emulador

¡Una vez completado este checklist, tu aplicación Maclaren estará lista para usar! 🎉
