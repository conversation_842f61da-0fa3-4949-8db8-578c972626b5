{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "maclaren", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#2196F3"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "config": {"googleMapsApiKey": "AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8"}, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Esta app necesita acceso a tu ubicación para mostrar tu posición en el mapa y conectarte con conductores cercanos.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Esta app necesita acceso a tu ubicación para mostrar tu posición en el mapa y conectarte con conductores cercanos.", "NSCameraUsageDescription": "Esta app necesita acceso a la cámara para tomar fotos de tus documentos de verificación.", "NSPhotoLibraryUsageDescription": "Esta app necesita acceso a tu galería para seleccionar fotos de tus documentos de verificación."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#2196F3"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "config": {"googleMaps": {"apiKey": "AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8"}}}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "<PERSON><PERSON><PERSON> use tu ubicación para conectarte con conductores y pasajeros cercanos."}], ["expo-image-picker", {"photosPermission": "La aplicación accede a tus fotos para permitirte subir documentos de verificación.", "cameraPermission": "La aplicación accede a tu cámara para permitirte tomar fotos de documentos de verificación."}]]}}