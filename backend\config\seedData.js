const mongoose = require('mongoose');
const User = require('../models/User');
const connectDB = require('./database');

const seedUsers = [
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
    phone: '+57 ************',
    address: 'Calle 123 #45-67, Bogotá',
    userType: 'both',
    balance: 25.00,
    documentsVerified: true,
    driverInfo: {
      vehicleType: 'motorcycle',
      vehiclePlate: 'ABC123',
      vehicleModel: 'Honda CB 150',
      vehicleYear: 2020,
      licenseNumber: 'L123456789',
      isAvailable: true,
      currentLocation: {
        latitude: 4.6097,
        longitude: -74.0817,
        updatedAt: new Date()
      }
    },
    stats: {
      totalTrips: 15,
      completedTrips: 14,
      cancelledTrips: 1,
      totalEarnings: 180.50
    }
  },
  {
    firstName: 'María',
    lastName: '<PERSON>',
    email: 'maria.gonza<PERSON><PERSON>@example.com',
    password: '123456',
    phone: '+57 ************',
    address: 'Carrera 45 #12-34, Medellín',
    userType: 'passenger',
    balance: 0,
    documentsVerified: true,
    stats: {
      totalTrips: 8,
      completedTrips: 8,
      cancelledTrips: 0,
      totalSpent: 65.00
    }
  },
  {
    firstName: 'Carlos',
    lastName: 'Rodríguez',
    email: '<EMAIL>',
    password: '123456',
    phone: '+57 ************',
    address: 'Avenida 68 #23-45, Cali',
    userType: 'driver',
    balance: 15.50,
    documentsVerified: true,
    driverInfo: {
      vehicleType: 'motorcycle',
      vehiclePlate: 'DEF456',
      vehicleModel: 'Yamaha FZ 150',
      vehicleYear: 2019,
      licenseNumber: 'L987654321',
      isAvailable: false,
      currentLocation: {
        latitude: 3.4516,
        longitude: -76.5320,
        updatedAt: new Date()
      }
    },
    stats: {
      totalTrips: 22,
      completedTrips: 20,
      cancelledTrips: 2,
      totalEarnings: 245.80
    }
  },
  {
    firstName: 'Ana',
    lastName: 'Martínez',
    email: '<EMAIL>',
    password: '123456',
    phone: '+57 ************',
    address: 'Calle 85 #15-30, Barranquilla',
    userType: 'passenger',
    balance: 0,
    documentsVerified: true,
    stats: {
      totalTrips: 12,
      completedTrips: 11,
      cancelledTrips: 1,
      totalSpent: 95.50
    }
  }
];

const seedDatabase = async () => {
  try {
    console.log('🌱 Iniciando seed de la base de datos...');
    
    await connectDB();
    
    // Limpiar datos existentes
    await User.deleteMany({});
    console.log('🗑️  Datos existentes eliminados');
    
    // Insertar usuarios de prueba
    const createdUsers = await User.insertMany(seedUsers);
    console.log(`👥 ${createdUsers.length} usuarios de prueba creados`);
    
    console.log('✅ Seed completado exitosamente');
    console.log('\n📋 Usuarios creados:');
    createdUsers.forEach(user => {
      console.log(`   - ${user.firstName} ${user.lastName} (${user.email}) - ${user.userType}`);
    });
    
    console.log('\n🔑 Credenciales de prueba:');
    console.log('   Email: <EMAIL> | Password: 123456 (Conductor/Pasajero)');
    console.log('   Email: <EMAIL> | Password: 123456 (Pasajero)');
    console.log('   Email: <EMAIL> | Password: 123456 (Conductor)');
    console.log('   Email: <EMAIL> | Password: 123456 (Pasajero)');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error en seed:', error);
    process.exit(1);
  }
};

// Ejecutar seed si se llama directamente
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase, seedUsers };
