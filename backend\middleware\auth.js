const jwt = require('jsonwebtoken');
const User = require('../models/User');

const auth = async (req, res, next) => {
  try {
    // Obtener token del header
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        message: 'No hay token, acceso denegado'
      });
    }

    // Verificar token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'maclaren_secret_key');
    
    // Buscar usuario
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        message: 'Token inválido'
      });
    }

    // Verificar si el usuario está activo
    if (!user.isActive) {
      return res.status(401).json({
        message: 'Cuenta desactivada'
      });
    }

    // Agregar usuario a la request
    req.user = decoded;
    req.userDoc = user;
    next();

  } catch (error) {
    console.error('Error en middleware de autenticación:', error);
    res.status(401).json({
      message: 'Token inválido'
    });
  }
};

// Middleware para verificar si el usuario puede ser conductor
const requireDriver = async (req, res, next) => {
  try {
    if (!req.userDoc) {
      return res.status(401).json({
        message: 'Usuario no autenticado'
      });
    }

    if (!req.userDoc.canBeDriver()) {
      return res.status(403).json({
        message: 'No tienes permisos de conductor o documentos no verificados'
      });
    }

    next();
  } catch (error) {
    console.error('Error en middleware de conductor:', error);
    res.status(500).json({
      message: 'Error interno del servidor'
    });
  }
};

// Middleware para verificar si el conductor puede recibir viajes
const requireActiveDriver = async (req, res, next) => {
  try {
    if (!req.userDoc) {
      return res.status(401).json({
        message: 'Usuario no autenticado'
      });
    }

    if (!req.userDoc.canReceiveTrips()) {
      return res.status(403).json({
        message: 'No puedes recibir viajes. Verifica tu saldo y disponibilidad'
      });
    }

    next();
  } catch (error) {
    console.error('Error en middleware de conductor activo:', error);
    res.status(500).json({
      message: 'Error interno del servidor'
    });
  }
};

module.exports = {
  auth,
  requireDriver,
  requireActiveDriver
};
