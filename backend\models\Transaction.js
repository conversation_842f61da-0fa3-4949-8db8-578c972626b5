const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['recharge', 'commission', 'payment', 'refund', 'withdrawal'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  // Referencia al viaje (si aplica)
  trip: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trip',
    default: null
  },
  // Información de pago externo
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash', 'balance'],
    default: 'balance'
  },
  externalTransactionId: {
    type: String,
    default: null
  },
  // Saldos antes y después de la transacción
  balanceBefore: {
    type: Number,
    required: true
  },
  balanceAfter: {
    type: Number,
    required: true
  },
  // Metadatos adicionales
  metadata: {
    gateway: String, // stripe, paypal, etc.
    gatewayTransactionId: String,
    gatewayResponse: mongoose.Schema.Types.Mixed,
    ipAddress: String,
    userAgent: String
  },
  // Información de comisión (para conductores)
  commissionInfo: {
    tripAmount: Number,
    commissionRate: {
      type: Number,
      default: 0.10 // 10%
    },
    commissionAmount: Number
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: Date,
  failedAt: Date,
  cancelledAt: Date
});

// Middleware para actualizar updatedAt
transactionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Método para marcar como completada
transactionSchema.methods.markAsCompleted = function() {
  this.status = 'completed';
  this.completedAt = new Date();
};

// Método para marcar como fallida
transactionSchema.methods.markAsFailed = function(reason = '') {
  this.status = 'failed';
  this.failedAt = new Date();
  if (reason) {
    this.description += ` - Error: ${reason}`;
  }
};

// Método para marcar como cancelada
transactionSchema.methods.markAsCancelled = function(reason = '') {
  this.status = 'cancelled';
  this.cancelledAt = new Date();
  if (reason) {
    this.description += ` - Cancelado: ${reason}`;
  }
};

// Método estático para crear transacción de recarga
transactionSchema.statics.createRecharge = function(userId, amount, balanceBefore, paymentMethod = 'balance') {
  return new this({
    user: userId,
    type: 'recharge',
    amount: amount,
    description: `Recarga de saldo por $${amount}`,
    balanceBefore: balanceBefore,
    balanceAfter: balanceBefore + amount,
    paymentMethod: paymentMethod,
    status: 'completed'
  });
};

// Método estático para crear transacción de comisión
transactionSchema.statics.createCommission = function(userId, tripId, tripAmount, balanceBefore) {
  const commissionRate = 0.10;
  const commissionAmount = tripAmount * commissionRate;
  
  return new this({
    user: userId,
    type: 'commission',
    amount: commissionAmount,
    description: `Comisión por viaje (10% de $${tripAmount})`,
    trip: tripId,
    balanceBefore: balanceBefore,
    balanceAfter: balanceBefore - commissionAmount,
    commissionInfo: {
      tripAmount: tripAmount,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount
    },
    status: 'completed'
  });
};

// Método estático para crear transacción de pago
transactionSchema.statics.createPayment = function(userId, tripId, amount, balanceBefore) {
  return new this({
    user: userId,
    type: 'payment',
    amount: amount,
    description: `Pago recibido por viaje`,
    trip: tripId,
    balanceBefore: balanceBefore,
    balanceAfter: balanceBefore + amount,
    status: 'completed'
  });
};

// Método estático para obtener estadísticas de usuario
transactionSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), status: 'completed' } },
    {
      $group: {
        _id: '$type',
        total: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    }
  ]);

  const result = {
    totalRecharges: 0,
    totalCommissions: 0,
    totalPayments: 0,
    rechargeCount: 0,
    commissionCount: 0,
    paymentCount: 0
  };

  stats.forEach(stat => {
    switch (stat._id) {
      case 'recharge':
        result.totalRecharges = stat.total;
        result.rechargeCount = stat.count;
        break;
      case 'commission':
        result.totalCommissions = stat.total;
        result.commissionCount = stat.count;
        break;
      case 'payment':
        result.totalPayments = stat.total;
        result.paymentCount = stat.count;
        break;
    }
  });

  return result;
};

// Índices para mejorar el rendimiento
transactionSchema.index({ user: 1 });
transactionSchema.index({ type: 1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ trip: 1 });
transactionSchema.index({ user: 1, createdAt: -1 });
transactionSchema.index({ user: 1, type: 1 });

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
