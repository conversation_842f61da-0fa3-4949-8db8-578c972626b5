const mongoose = require('mongoose');

const locationSchema = new mongoose.Schema({
  latitude: {
    type: Number,
    required: true
  },
  longitude: {
    type: Number,
    required: true
  },
  address: {
    type: String,
    default: ''
  }
}, { _id: false });

const tripSchema = new mongoose.Schema({
  passenger: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  driver: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  pickupLocation: {
    type: locationSchema,
    required: true
  },
  destination: {
    type: locationSchema,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'in_progress', 'completed', 'cancelled'],
    default: 'pending'
  },
  offeredPrice: {
    type: Number,
    required: true,
    min: 0
  },
  finalPrice: {
    type: Number,
    default: null
  },
  commission: {
    type: Number,
    default: 0
  },
  distance: {
    type: Number, // en kilómetros
    default: 0
  },
  estimatedDuration: {
    type: Number, // en minutos
    default: 0
  },
  actualDuration: {
    type: Number, // en minutos
    default: 0
  },
  passengerNotes: {
    type: String,
    default: ''
  },
  driverNotes: {
    type: String,
    default: ''
  },
  // Seguimiento del viaje
  timeline: [{
    status: {
      type: String,
      enum: ['requested', 'accepted', 'driver_arrived', 'started', 'completed', 'cancelled']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    location: locationSchema,
    notes: String
  }],
  // Ubicaciones del conductor durante el viaje
  driverRoute: [{
    latitude: Number,
    longitude: Number,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  // Calificaciones
  passengerRating: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    ratedAt: Date
  },
  driverRating: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    ratedAt: Date
  },
  // Información de pago
  paymentInfo: {
    method: {
      type: String,
      enum: ['balance', 'cash', 'card'],
      default: 'balance'
    },
    transactionId: String,
    paidAt: Date
  },
  // Razón de cancelación
  cancellationReason: {
    reason: String,
    cancelledBy: {
      type: String,
      enum: ['passenger', 'driver', 'system']
    },
    cancelledAt: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Timestamps específicos
  acceptedAt: Date,
  startedAt: Date,
  completedAt: Date,
  cancelledAt: Date
});

// Middleware para actualizar updatedAt
tripSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Método para calcular la comisión (10%)
tripSchema.methods.calculateCommission = function() {
  const price = this.finalPrice || this.offeredPrice;
  this.commission = price * 0.10;
  return this.commission;
};

// Método para agregar evento al timeline
tripSchema.methods.addTimelineEvent = function(status, location = null, notes = '') {
  this.timeline.push({
    status,
    location,
    notes,
    timestamp: new Date()
  });
};

// Método para actualizar el estado del viaje
tripSchema.methods.updateStatus = function(newStatus, location = null, notes = '') {
  this.status = newStatus;
  this.addTimelineEvent(newStatus, location, notes);
  
  // Actualizar timestamps específicos
  switch (newStatus) {
    case 'accepted':
      this.acceptedAt = new Date();
      break;
    case 'in_progress':
      this.startedAt = new Date();
      break;
    case 'completed':
      this.completedAt = new Date();
      this.finalPrice = this.finalPrice || this.offeredPrice;
      this.calculateCommission();
      break;
    case 'cancelled':
      this.cancelledAt = new Date();
      break;
  }
};

// Método para agregar ubicación del conductor
tripSchema.methods.addDriverLocation = function(latitude, longitude) {
  this.driverRoute.push({
    latitude,
    longitude,
    timestamp: new Date()
  });
};

// Método para calcular la duración del viaje
tripSchema.methods.calculateDuration = function() {
  if (this.startedAt && this.completedAt) {
    const duration = (this.completedAt - this.startedAt) / (1000 * 60); // en minutos
    this.actualDuration = Math.round(duration);
    return this.actualDuration;
  }
  return 0;
};

// Método para verificar si el viaje puede ser cancelado
tripSchema.methods.canBeCancelled = function() {
  return ['pending', 'accepted'].includes(this.status);
};

// Método para verificar si el viaje puede ser calificado
tripSchema.methods.canBeRated = function() {
  return this.status === 'completed';
};

// Índices para mejorar el rendimiento
tripSchema.index({ passenger: 1 });
tripSchema.index({ driver: 1 });
tripSchema.index({ status: 1 });
tripSchema.index({ createdAt: -1 });
tripSchema.index({ 'pickupLocation.latitude': 1, 'pickupLocation.longitude': 1 });
tripSchema.index({ 
  'pickupLocation.latitude': 1, 
  'pickupLocation.longitude': 1, 
  status: 1 
});

// Índice geoespacial para búsquedas por ubicación
tripSchema.index({ pickupLocation: '2dsphere' });

const Trip = mongoose.model('Trip', tripSchema);

module.exports = Trip;
