const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const documentSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['cedula', 'reciboPago', 'registroVehicular', 'soat', 'licenciaConducir', 'placaVehiculo']
  },
  filename: {
    type: String,
    required: true
  },
  originalName: {
    type: String,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  verified: {
    type: Boolean,
    default: false
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  }
});

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    type: String,
    required: true,
    trim: true
  },
  userType: {
    type: String,
    required: true,
    enum: ['passenger', 'driver', 'both'],
    default: 'passenger'
  },
  documents: [documentSchema],
  documentsVerified: {
    type: Boolean,
    default: false
  },
  balance: {
    type: Number,
    default: 0,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  // Información específica del conductor
  driverInfo: {
    vehicleType: {
      type: String,
      enum: ['motorcycle', 'car'],
      default: 'motorcycle'
    },
    vehiclePlate: String,
    vehicleModel: String,
    vehicleYear: Number,
    licenseNumber: String,
    soatExpiry: Date,
    registrationExpiry: Date,
    isAvailable: {
      type: Boolean,
      default: false
    },
    currentLocation: {
      latitude: Number,
      longitude: Number,
      updatedAt: Date
    }
  },
  // Estadísticas
  stats: {
    totalTrips: {
      type: Number,
      default: 0
    },
    completedTrips: {
      type: Number,
      default: 0
    },
    cancelledTrips: {
      type: Number,
      default: 0
    },
    totalEarnings: {
      type: Number,
      default: 0
    },
    totalSpent: {
      type: Number,
      default: 0
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Middleware para hashear la contraseña antes de guardar
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Middleware para actualizar updatedAt
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Método para comparar contraseñas
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Método para obtener información pública del usuario
userSchema.methods.toPublicJSON = function() {
  const user = this.toObject();
  delete user.password;
  delete user.__v;
  return user;
};

// Método para verificar si el usuario puede ser conductor
userSchema.methods.canBeDriver = function() {
  const requiredDocs = ['cedula', 'reciboPago', 'registroVehicular', 'soat', 'licenciaConducir'];
  const userDocs = this.documents.map(doc => doc.type);
  
  return requiredDocs.every(docType => userDocs.includes(docType)) && 
         this.documentsVerified && 
         this.balance >= 10;
};

// Método para verificar si el usuario puede recibir viajes como conductor
userSchema.methods.canReceiveTrips = function() {
  return this.canBeDriver() && 
         this.driverInfo.isAvailable && 
         this.balance >= 10;
};

// Índices para mejorar el rendimiento
userSchema.index({ email: 1 });
userSchema.index({ 'driverInfo.currentLocation': '2dsphere' });
userSchema.index({ 'driverInfo.isAvailable': 1 });

const User = mongoose.model('User', userSchema);

module.exports = User;
