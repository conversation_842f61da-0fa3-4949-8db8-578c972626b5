const express = require('express');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const User = require('../models/User');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Configuración de multer para subida de archivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/documents/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Solo se permiten archivos de imagen (JPEG, JPG, PNG) o PDF'));
    }
  }
});

// Generar JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET || 'maclaren_secret_key', {
    expiresIn: '30d'
  });
};

// @route   POST /api/auth/register
// @desc    Registrar nuevo usuario
// @access  Public
router.post('/register', async (req, res) => {
  try {
    const { firstName, lastName, email, password, phone, address, userType } = req.body;

    // Validar campos requeridos
    if (!firstName || !lastName || !email || !password || !phone || !address) {
      return res.status(400).json({
        message: 'Todos los campos son requeridos'
      });
    }

    // Verificar si el usuario ya existe
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        message: 'Ya existe un usuario con este correo electrónico'
      });
    }

    // Crear nuevo usuario
    const user = new User({
      firstName,
      lastName,
      email,
      password,
      phone,
      address,
      userType: userType || 'passenger'
    });

    await user.save();

    res.status(201).json({
      message: 'Usuario registrado exitosamente',
      user: user.toPublicJSON()
    });

  } catch (error) {
    console.error('Error en registro:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/auth/login
// @desc    Iniciar sesión
// @access  Public
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validar campos requeridos
    if (!email || !password) {
      return res.status(400).json({
        message: 'Email y contraseña son requeridos'
      });
    }

    // Buscar usuario
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        message: 'Credenciales inválidas'
      });
    }

    // Verificar contraseña
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({
        message: 'Credenciales inválidas'
      });
    }

    // Verificar si el usuario está activo
    if (!user.isActive) {
      return res.status(401).json({
        message: 'Cuenta desactivada. Contacta al soporte'
      });
    }

    // Generar token
    const token = generateToken(user._id);

    res.json({
      message: 'Inicio de sesión exitoso',
      token,
      user: user.toPublicJSON()
    });

  } catch (error) {
    console.error('Error en login:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/auth/upload-documents
// @desc    Subir documentos de verificación
// @access  Public
router.post('/upload-documents', upload.fields([
  { name: 'cedula', maxCount: 1 },
  { name: 'reciboPago', maxCount: 1 },
  { name: 'registroVehicular', maxCount: 1 },
  { name: 'soat', maxCount: 1 },
  { name: 'licenciaConducir', maxCount: 1 },
  { name: 'placaVehiculo', maxCount: 1 }
]), async (req, res) => {
  try {
    const { userData } = req.body;
    
    if (!userData) {
      return res.status(400).json({
        message: 'Datos de usuario requeridos'
      });
    }

    const userInfo = JSON.parse(userData);

    // Buscar usuario por email
    const user = await User.findOne({ email: userInfo.email });
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    // Procesar archivos subidos
    const documents = [];
    
    Object.keys(req.files).forEach(fieldName => {
      const file = req.files[fieldName][0];
      documents.push({
        type: fieldName,
        filename: file.filename,
        originalName: file.originalname,
        path: file.path,
        verified: false
      });
    });

    // Actualizar documentos del usuario
    user.documents = documents;
    await user.save();

    res.json({
      message: 'Documentos subidos exitosamente',
      documents: documents.length
    });

  } catch (error) {
    console.error('Error subiendo documentos:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/auth/verify-documents/:userId
// @desc    Verificar estado de documentos
// @access  Private
router.get('/verify-documents/:userId', auth, async (req, res) => {
  try {
    const user = await User.findById(req.params.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    res.json({
      verified: user.documentsVerified,
      documents: user.documents.map(doc => ({
        type: doc.type,
        verified: doc.verified,
        uploadedAt: doc.uploadedAt
      }))
    });

  } catch (error) {
    console.error('Error verificando documentos:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/auth/me
// @desc    Obtener información del usuario actual
// @access  Private
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    res.json({
      user: user.toPublicJSON()
    });

  } catch (error) {
    console.error('Error obteniendo usuario:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

module.exports = router;
