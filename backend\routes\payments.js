const express = require('express');
const User = require('../models/User');
const Transaction = require('../models/Transaction');
const { auth, requireDriver } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/payments/recharge
// @desc    Recargar saldo del usuario
// @access  Private
router.post('/recharge', auth, async (req, res) => {
  try {
    const { amount = 10 } = req.body;

    // Validar monto mínimo
    if (amount < 1) {
      return res.status(400).json({
        message: 'El monto mínimo de recarga es $1'
      });
    }

    // Validar monto máximo
    if (amount > 1000) {
      return res.status(400).json({
        message: 'El monto máximo de recarga es $1000'
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    const previousBalance = user.balance;

    // Simular procesamiento de pago
    // En producción, aquí integrarías con un gateway de pagos real
    const paymentResult = await simulatePaymentProcessing(amount);
    
    if (!paymentResult.success) {
      return res.status(400).json({
        message: 'Error procesando el pago',
        error: paymentResult.error
      });
    }

    // Actualizar saldo del usuario
    user.balance += amount;
    await user.save();

    // Crear transacción
    const transaction = Transaction.createRecharge(
      user._id,
      amount,
      previousBalance,
      'credit_card'
    );
    transaction.externalTransactionId = paymentResult.transactionId;
    transaction.markAsCompleted();
    await transaction.save();

    res.json({
      message: 'Recarga exitosa',
      balance: user.balance,
      transactionId: transaction._id,
      amount: amount
    });

  } catch (error) {
    console.error('Error en recarga:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/payments/balance/:userId
// @desc    Obtener saldo del usuario
// @access  Private
router.get('/balance/:userId', auth, async (req, res) => {
  try {
    // Verificar que el usuario solo pueda ver su propio saldo
    if (req.params.userId !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para ver este saldo'
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    res.json({
      balance: user.balance,
      canReceiveTrips: user.canReceiveTrips()
    });

  } catch (error) {
    console.error('Error obteniendo saldo:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/payments/commission
// @desc    Procesar comisión por viaje
// @access  Private (Sistema interno)
router.post('/commission', auth, requireDriver, async (req, res) => {
  try {
    const { tripId, tripAmount, commission } = req.body;

    if (!tripId || !tripAmount || !commission) {
      return res.status(400).json({
        message: 'Datos de comisión incompletos'
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    const previousBalance = user.balance;

    // Verificar que el usuario tenga saldo suficiente
    if (user.balance < commission) {
      return res.status(400).json({
        message: 'Saldo insuficiente para pagar la comisión',
        needsRecharge: true
      });
    }

    // Descontar comisión
    user.balance -= commission;
    await user.save();

    // Crear transacción de comisión
    const transaction = Transaction.createCommission(
      user._id,
      tripId,
      tripAmount,
      previousBalance
    );
    transaction.markAsCompleted();
    await transaction.save();

    res.json({
      message: 'Comisión procesada exitosamente',
      commission: commission,
      remainingBalance: user.balance,
      needsRecharge: user.balance < 10
    });

  } catch (error) {
    console.error('Error procesando comisión:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/payments/history/:userId
// @desc    Obtener historial de transacciones
// @access  Private
router.get('/history/:userId', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20, type } = req.query;

    // Verificar que el usuario solo pueda ver su propio historial
    if (req.params.userId !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para ver este historial'
      });
    }

    // Construir filtros
    const filters = { user: req.user.userId };
    if (type) {
      filters.type = type;
    }

    const transactions = await Transaction.find(filters)
      .populate('trip', 'pickupLocation destination offeredPrice status')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Transaction.countDocuments(filters);

    res.json({
      transactions,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });

  } catch (error) {
    console.error('Error obteniendo historial de transacciones:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/payments/check-balance/:userId
// @desc    Verificar si el conductor puede recibir viajes
// @access  Private
router.get('/check-balance/:userId', auth, async (req, res) => {
  try {
    // Verificar que el usuario solo pueda verificar su propio saldo
    if (req.params.userId !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para verificar este saldo'
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    const canReceiveTrips = user.canReceiveTrips();
    const needsRecharge = user.balance < 10;

    res.json({
      canReceiveTrips,
      balance: user.balance,
      needsRecharge,
      minimumBalance: 10
    });

  } catch (error) {
    console.error('Error verificando saldo:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/payments/process
// @desc    Procesar pago genérico
// @access  Private
router.post('/process', auth, async (req, res) => {
  try {
    const { amount, paymentMethod, description } = req.body;

    if (!amount || !paymentMethod) {
      return res.status(400).json({
        message: 'Monto y método de pago son requeridos'
      });
    }

    // Simular procesamiento de pago
    const paymentResult = await simulatePaymentProcessing(amount, paymentMethod);
    
    if (!paymentResult.success) {
      return res.status(400).json({
        message: 'Error procesando el pago',
        error: paymentResult.error
      });
    }

    res.json({
      message: 'Pago procesado exitosamente',
      transactionId: paymentResult.transactionId,
      status: 'completed'
    });

  } catch (error) {
    console.error('Error procesando pago:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/payments/stats/:userId
// @desc    Obtener estadísticas de pagos del usuario
// @access  Private
router.get('/stats/:userId', auth, async (req, res) => {
  try {
    // Verificar que el usuario solo pueda ver sus propias estadísticas
    if (req.params.userId !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para ver estas estadísticas'
      });
    }

    const stats = await Transaction.getUserStats(req.user.userId);
    
    res.json({
      stats,
      currentBalance: req.userDoc.balance
    });

  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Función auxiliar para simular procesamiento de pagos
async function simulatePaymentProcessing(amount, method = 'credit_card') {
  // Simular delay de procesamiento
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simular éxito/fallo (95% éxito)
  const success = Math.random() > 0.05;
  
  if (success) {
    return {
      success: true,
      transactionId: `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      method: method
    };
  } else {
    return {
      success: false,
      error: 'Pago rechazado por el banco'
    };
  }
}

module.exports = router;
