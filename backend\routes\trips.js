const express = require('express');
const Trip = require('../models/Trip');
const User = require('../models/User');
const Transaction = require('../models/Transaction');
const { auth, requireDriver } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/trips/request
// @desc    Crear solicitud de viaje
// @access  Private
router.post('/request', auth, async (req, res) => {
  try {
    const { pickupLocation, destination, offeredPrice, passengerNotes } = req.body;

    // Validar campos requeridos
    if (!pickupLocation || !destination || !offeredPrice) {
      return res.status(400).json({
        message: 'Ubicación de recogida, destino y precio son requeridos'
      });
    }

    // Validar precio mínimo
    if (offeredPrice < 1) {
      return res.status(400).json({
        message: 'El precio mínimo es $1'
      });
    }

    // Crear nuevo viaje
    const trip = new Trip({
      passenger: req.user.userId,
      pickupLocation,
      destination,
      offeredPrice,
      passengerNotes: passengerNotes || ''
    });

    // Agregar evento inicial al timeline
    trip.addTimelineEvent('requested', pickupLocation, 'Viaje solicitado');

    await trip.save();

    // Poblar información del pasajero
    await trip.populate('passenger', 'firstName lastName phone');

    // Emitir evento de nuevo viaje disponible (Socket.IO)
    const io = req.app.get('io');
    if (io) {
      io.emit('trip-available', {
      tripId: trip._id,
      passenger: trip.passenger,
      pickupLocation: trip.pickupLocation,
      destination: trip.destination,
      offeredPrice: trip.offeredPrice,
      passengerNotes: trip.passengerNotes
      });
    }

    res.status(201).json({
      message: 'Solicitud de viaje creada exitosamente',
      trip
    });

  } catch (error) {
    console.error('Error creando solicitud de viaje:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/trips/available
// @desc    Obtener viajes disponibles para conductores
// @access  Private (Conductores)
router.get('/available', auth, requireDriver, async (req, res) => {
  try {
    const { latitude, longitude, radius = 10 } = req.query;

    if (!latitude || !longitude) {
      return res.status(400).json({
        message: 'Ubicación del conductor requerida'
      });
    }

    // Buscar viajes pendientes cerca de la ubicación del conductor
    const trips = await Trip.find({
      status: 'pending',
      'pickupLocation.latitude': {
        $gte: parseFloat(latitude) - (radius / 111), // Aproximación: 1 grado ≈ 111 km
        $lte: parseFloat(latitude) + (radius / 111)
      },
      'pickupLocation.longitude': {
        $gte: parseFloat(longitude) - (radius / 111),
        $lte: parseFloat(longitude) + (radius / 111)
      }
    })
    .populate('passenger', 'firstName lastName phone rating')
    .sort({ createdAt: -1 })
    .limit(20);

    res.json({
      trips,
      count: trips.length
    });

  } catch (error) {
    console.error('Error obteniendo viajes disponibles:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/trips/:tripId/accept
// @desc    Aceptar un viaje
// @access  Private (Conductores)
router.post('/:tripId/accept', auth, requireDriver, async (req, res) => {
  try {
    const trip = await Trip.findById(req.params.tripId);
    
    if (!trip) {
      return res.status(404).json({
        message: 'Viaje no encontrado'
      });
    }

    if (trip.status !== 'pending') {
      return res.status(400).json({
        message: 'Este viaje ya no está disponible'
      });
    }

    // Verificar que el conductor tenga saldo suficiente
    if (req.userDoc.balance < 10) {
      return res.status(400).json({
        message: 'Saldo insuficiente para aceptar viajes'
      });
    }

    // Asignar conductor y cambiar estado
    trip.driver = req.user.userId;
    trip.updateStatus('accepted', req.userDoc.driverInfo.currentLocation, 'Viaje aceptado por conductor');

    await trip.save();

    // Poblar información completa
    await trip.populate([
      { path: 'passenger', select: 'firstName lastName phone' },
      { path: 'driver', select: 'firstName lastName phone driverInfo' }
    ]);

    // Emitir evento de viaje aceptado
    req.app.get('io')?.to(trip._id.toString()).emit('trip-accepted', {
      tripId: trip._id,
      driver: trip.driver
    });

    res.json({
      message: 'Viaje aceptado exitosamente',
      trip
    });

  } catch (error) {
    console.error('Error aceptando viaje:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/trips/:tripId/start
// @desc    Iniciar un viaje
// @access  Private (Conductores)
router.post('/:tripId/start', auth, requireDriver, async (req, res) => {
  try {
    const trip = await Trip.findById(req.params.tripId);
    
    if (!trip) {
      return res.status(404).json({
        message: 'Viaje no encontrado'
      });
    }

    if (trip.driver.toString() !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para este viaje'
      });
    }

    if (trip.status !== 'accepted') {
      return res.status(400).json({
        message: 'El viaje debe estar aceptado para poder iniciarlo'
      });
    }

    // Cambiar estado a en progreso
    trip.updateStatus('in_progress', trip.pickupLocation, 'Viaje iniciado');

    await trip.save();

    // Emitir evento de viaje iniciado
    req.app.get('io')?.to(trip._id.toString()).emit('trip-started', {
      tripId: trip._id,
      startedAt: trip.startedAt
    });

    res.json({
      message: 'Viaje iniciado exitosamente',
      trip
    });

  } catch (error) {
    console.error('Error iniciando viaje:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/trips/:tripId/complete
// @desc    Completar un viaje
// @access  Private (Conductores)
router.post('/:tripId/complete', auth, requireDriver, async (req, res) => {
  try {
    const trip = await Trip.findById(req.params.tripId);
    
    if (!trip) {
      return res.status(404).json({
        message: 'Viaje no encontrado'
      });
    }

    if (trip.driver.toString() !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para este viaje'
      });
    }

    if (trip.status !== 'in_progress') {
      return res.status(400).json({
        message: 'El viaje debe estar en progreso para completarlo'
      });
    }

    // Completar viaje
    trip.updateStatus('completed', trip.destination, 'Viaje completado');
    trip.calculateDuration();

    // Procesar pago y comisión
    const driver = await User.findById(trip.driver);
    const commission = trip.commission;
    
    // Descontar comisión del conductor
    const newBalance = driver.balance - commission;
    driver.balance = Math.max(0, newBalance);
    
    // Actualizar estadísticas
    driver.stats.completedTrips += 1;
    driver.stats.totalEarnings += (trip.offeredPrice - commission);
    
    await driver.save();

    // Crear transacción de comisión
    const commissionTransaction = Transaction.createCommission(
      driver._id,
      trip._id,
      trip.offeredPrice,
      driver.balance + commission
    );
    await commissionTransaction.save();

    await trip.save();

    // Emitir evento de viaje completado
    req.app.get('io')?.to(trip._id.toString()).emit('trip-completed', {
      tripId: trip._id,
      completedAt: trip.completedAt,
      commission: commission
    });

    res.json({
      message: 'Viaje completado exitosamente',
      trip,
      commission,
      needsRecharge: driver.balance < 10
    });

  } catch (error) {
    console.error('Error completando viaje:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/trips/:tripId/cancel
// @desc    Cancelar un viaje
// @access  Private
router.post('/:tripId/cancel', auth, async (req, res) => {
  try {
    const { reason } = req.body;
    const trip = await Trip.findById(req.params.tripId);
    
    if (!trip) {
      return res.status(404).json({
        message: 'Viaje no encontrado'
      });
    }

    // Verificar permisos
    const isPassenger = trip.passenger.toString() === req.user.userId;
    const isDriver = trip.driver && trip.driver.toString() === req.user.userId;
    
    if (!isPassenger && !isDriver) {
      return res.status(403).json({
        message: 'No tienes permisos para cancelar este viaje'
      });
    }

    if (!trip.canBeCancelled()) {
      return res.status(400).json({
        message: 'Este viaje no puede ser cancelado'
      });
    }

    // Cancelar viaje
    trip.updateStatus('cancelled', null, reason || 'Viaje cancelado');
    trip.cancellationReason = {
      reason: reason || 'Sin razón especificada',
      cancelledBy: isPassenger ? 'passenger' : 'driver',
      cancelledAt: new Date()
    };

    await trip.save();

    // Actualizar estadísticas
    if (isDriver) {
      await User.findByIdAndUpdate(trip.driver, {
        $inc: { 'stats.cancelledTrips': 1 }
      });
    }

    // Emitir evento de viaje cancelado
    req.app.get('io')?.to(trip._id.toString()).emit('trip-cancelled', {
      tripId: trip._id,
      cancelledBy: isPassenger ? 'passenger' : 'driver',
      reason: reason
    });

    res.json({
      message: 'Viaje cancelado exitosamente',
      trip
    });

  } catch (error) {
    console.error('Error cancelando viaje:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/trips/:tripId/location
// @desc    Actualizar ubicación del conductor
// @access  Private (Conductores)
router.post('/:tripId/location', auth, requireDriver, async (req, res) => {
  try {
    const { latitude, longitude } = req.body;
    const trip = await Trip.findById(req.params.tripId);
    
    if (!trip) {
      return res.status(404).json({
        message: 'Viaje no encontrado'
      });
    }

    if (trip.driver.toString() !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para este viaje'
      });
    }

    // Agregar ubicación a la ruta
    trip.addDriverLocation(latitude, longitude);
    await trip.save();

    // Actualizar ubicación del conductor
    await User.findByIdAndUpdate(req.user.userId, {
      'driverInfo.currentLocation': {
        latitude,
        longitude,
        updatedAt: new Date()
      }
    });

    // Emitir ubicación actualizada
    req.app.get('io')?.to(trip._id.toString()).emit('driver-location-updated', {
      latitude,
      longitude,
      timestamp: new Date()
    });

    res.json({
      message: 'Ubicación actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error actualizando ubicación:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/trips/history/:userId
// @desc    Obtener historial de viajes
// @access  Private
router.get('/history/:userId', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    
    // Verificar que el usuario solo pueda ver su propio historial
    if (req.params.userId !== req.user.userId) {
      return res.status(403).json({
        message: 'No tienes permisos para ver este historial'
      });
    }

    const trips = await Trip.find({
      $or: [
        { passenger: req.user.userId },
        { driver: req.user.userId }
      ]
    })
    .populate('passenger', 'firstName lastName phone')
    .populate('driver', 'firstName lastName phone')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

    const total = await Trip.countDocuments({
      $or: [
        { passenger: req.user.userId },
        { driver: req.user.userId }
      ]
    });

    res.json({
      trips,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });

  } catch (error) {
    console.error('Error obteniendo historial:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

module.exports = router;
