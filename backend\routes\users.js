const express = require('express');
const User = require('../models/User');
const { auth, requireDriver } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/users/profile
// @desc    Obtener perfil del usuario actual
// @access  Private
router.get('/profile', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-password');
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    res.json({
      user: user.toPublicJSON()
    });

  } catch (error) {
    console.error('Error obteniendo perfil:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   PUT /api/users/profile
// @desc    Actualizar perfil del usuario
// @access  Private
router.put('/profile', auth, async (req, res) => {
  try {
    const { firstName, lastName, phone, address } = req.body;
    
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    // Actualizar campos permitidos
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (phone) user.phone = phone;
    if (address) user.address = address;

    await user.save();

    res.json({
      message: 'Perfil actualizado exitosamente',
      user: user.toPublicJSON()
    });

  } catch (error) {
    console.error('Error actualizando perfil:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   PUT /api/users/driver-availability
// @desc    Cambiar disponibilidad del conductor
// @access  Private (Conductores)
router.put('/driver-availability', auth, requireDriver, async (req, res) => {
  try {
    const { isAvailable, latitude, longitude } = req.body;
    
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    // Verificar que puede recibir viajes si se está poniendo disponible
    if (isAvailable && !user.canReceiveTrips()) {
      return res.status(400).json({
        message: 'No puedes ponerte disponible. Verifica tu saldo y documentos.'
      });
    }

    // Actualizar disponibilidad
    user.driverInfo.isAvailable = isAvailable;
    
    // Actualizar ubicación si se proporciona
    if (latitude && longitude) {
      user.driverInfo.currentLocation = {
        latitude,
        longitude,
        updatedAt: new Date()
      };
    }

    await user.save();

    res.json({
      message: `Disponibilidad ${isAvailable ? 'activada' : 'desactivada'} exitosamente`,
      isAvailable: user.driverInfo.isAvailable,
      location: user.driverInfo.currentLocation
    });

  } catch (error) {
    console.error('Error cambiando disponibilidad:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   PUT /api/users/driver-location
// @desc    Actualizar ubicación del conductor
// @access  Private (Conductores)
router.put('/driver-location', auth, requireDriver, async (req, res) => {
  try {
    const { latitude, longitude } = req.body;
    
    if (!latitude || !longitude) {
      return res.status(400).json({
        message: 'Latitud y longitud son requeridas'
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    // Actualizar ubicación
    user.driverInfo.currentLocation = {
      latitude,
      longitude,
      updatedAt: new Date()
    };

    await user.save();

    res.json({
      message: 'Ubicación actualizada exitosamente',
      location: user.driverInfo.currentLocation
    });

  } catch (error) {
    console.error('Error actualizando ubicación:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/users/stats
// @desc    Obtener estadísticas del usuario
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    res.json({
      stats: user.stats,
      rating: user.rating,
      balance: user.balance,
      documentsVerified: user.documentsVerified
    });

  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   PUT /api/users/driver-info
// @desc    Actualizar información del vehículo
// @access  Private (Conductores)
router.put('/driver-info', auth, requireDriver, async (req, res) => {
  try {
    const { 
      vehicleType, 
      vehiclePlate, 
      vehicleModel, 
      vehicleYear, 
      licenseNumber 
    } = req.body;
    
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        message: 'Usuario no encontrado'
      });
    }

    // Actualizar información del vehículo
    if (vehicleType) user.driverInfo.vehicleType = vehicleType;
    if (vehiclePlate) user.driverInfo.vehiclePlate = vehiclePlate;
    if (vehicleModel) user.driverInfo.vehicleModel = vehicleModel;
    if (vehicleYear) user.driverInfo.vehicleYear = vehicleYear;
    if (licenseNumber) user.driverInfo.licenseNumber = licenseNumber;

    await user.save();

    res.json({
      message: 'Información del vehículo actualizada exitosamente',
      driverInfo: user.driverInfo
    });

  } catch (error) {
    console.error('Error actualizando información del conductor:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

module.exports = router;
