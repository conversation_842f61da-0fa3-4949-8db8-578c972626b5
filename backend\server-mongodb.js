require('dotenv').config();
const express = require('express');
const cors = require('cors');
const connectDB = require('./config/database');

// Importar modelos
const User = require('./models/User');
const Trip = require('./models/Trip');

const app = express();
const PORT = process.env.PORT || 3000;

// Conectar a MongoDB
connectDB();

// Middleware
app.use(cors());
app.use(express.json());

// Ruta de salud
app.get('/api/health', (req, res) => {
  res.json({
    message: 'Servidor Maclaren funcionando correctamente con MongoDB Atlas',
    timestamp: new Date().toISOString(),
    mode: 'production',
    database: 'MongoDB Atlas'
  });
});

// Ruta de registro
app.post('/api/auth/register', async (req, res) => {
  try {
    const { firstName, lastName, email, password, phone, address, userType } = req.body;

    if (!firstName || !lastName || !email || !password || !phone || !address) {
      return res.status(400).json({
        message: 'Todos los campos son requeridos'
      });
    }

    // Verificar si el usuario ya existe
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        message: 'Ya existe un usuario con este correo electrónico'
      });
    }

    // Crear nuevo usuario
    const newUser = new User({
      firstName,
      lastName,
      email,
      password, // En producción, esto debería estar hasheado
      phone,
      address,
      userType: userType || 'passenger',
      isActive: true,
      balance: userType === 'driver' ? 10 : 0, // Dar saldo inicial a conductores
      documentsVerified: false
    });

    await newUser.save();

    res.status(201).json({
      message: 'Usuario registrado exitosamente',
      user: {
        id: newUser._id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        phone: newUser.phone,
        userType: newUser.userType,
        balance: newUser.balance,
        documentsVerified: newUser.documentsVerified
      }
    });

  } catch (error) {
    console.error('Error en registro:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Ruta de login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        message: 'Email y contraseña son requeridos'
      });
    }

    // Buscar usuario
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        message: 'Credenciales inválidas'
      });
    }

    // Verificar contraseña usando bcrypt
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        message: 'Credenciales inválidas'
      });
    }

    res.json({
      message: 'Login exitoso',
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        userType: user.userType,
        balance: user.balance,
        documentsVerified: user.documentsVerified
      },
      token: 'fake-jwt-token' // En producción, generar JWT real
    });

  } catch (error) {
    console.error('Error en login:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Rutas adicionales para compatibilidad
app.get('/api/users/profile', async (req, res) => {
  try {
    const users = await User.find().limit(1);
    res.json({
      message: 'Perfil de usuario',
      user: users[0] || null
    });
  } catch (error) {
    res.status(500).json({ message: 'Error al obtener perfil', error: error.message });
  }
});

app.get('/api/trips/history/:userId', async (req, res) => {
  try {
    const trips = await Trip.find({
      $or: [
        { passenger: req.params.userId },
        { driver: req.params.userId }
      ]
    }).sort({ createdAt: -1 });

    res.json({
      trips,
      message: 'Historial de viajes obtenido'
    });
  } catch (error) {
    res.status(500).json({ message: 'Error al obtener historial', error: error.message });
  }
});

app.post('/api/payments/recharge', async (req, res) => {
  try {
    const { amount = 10, userId } = req.body;
    
    if (userId) {
      const user = await User.findById(userId);
      if (user) {
        user.balance += amount;
        await user.save();
        
        return res.json({
          message: 'Recarga exitosa',
          balance: user.balance,
          transactionId: `txn_${Date.now()}`
        });
      }
    }

    res.json({
      message: 'Recarga simulada exitosa',
      balance: 100,
      transactionId: `txn_${Date.now()}`
    });
  } catch (error) {
    res.status(500).json({ message: 'Error en recarga', error: error.message });
  }
});

// Ruta para crear solicitud de viaje
app.post('/api/trips/request', async (req, res) => {
  try {
    const { pickupLocation, destination, offeredPrice, passengerNotes, passengerId } = req.body;

    if (!pickupLocation || !destination || !offeredPrice) {
      return res.status(400).json({
        message: 'Ubicación de recogida, destino y precio son requeridos'
      });
    }

    const newTrip = new Trip({
      passenger: passengerId || '685233914eb58a9853a78d5f', // Usuario de prueba
      pickupLocation: {
        latitude: pickupLocation.latitude,
        longitude: pickupLocation.longitude,
        address: pickupLocation.address || 'Ubicación de recogida'
      },
      destination: {
        latitude: destination.latitude,
        longitude: destination.longitude,
        address: destination.address || 'Destino'
      },
      offeredPrice,
      passengerNotes: passengerNotes || '',
      status: 'pending'
    });

    await newTrip.save();

    res.status(201).json({
      message: 'Solicitud de viaje creada exitosamente',
      trip: newTrip
    });

  } catch (error) {
    console.error('Error creando solicitud de viaje:', error);
    res.status(500).json({
      message: 'Error al crear solicitud de viaje',
      error: error.message
    });
  }
});

// Ruta para obtener viajes disponibles (para conductores)
app.get('/api/trips/available', async (req, res) => {
  try {
    const { latitude, longitude, radius = 10 } = req.query;

    if (!latitude || !longitude) {
      return res.status(400).json({
        message: 'Latitud y longitud son requeridas'
      });
    }

    // Buscar viajes pendientes cerca de la ubicación del conductor
    const availableTrips = await Trip.find({
      status: 'pending',
      'pickupLocation.latitude': {
        $gte: parseFloat(latitude) - (radius / 111), // Aproximación: 1 grado ≈ 111 km
        $lte: parseFloat(latitude) + (radius / 111)
      },
      'pickupLocation.longitude': {
        $gte: parseFloat(longitude) - (radius / 111),
        $lte: parseFloat(longitude) + (radius / 111)
      }
    }).populate('passenger', 'firstName lastName phone').sort({ createdAt: -1 });

    res.json({
      trips: availableTrips,
      message: `${availableTrips.length} viajes disponibles encontrados`
    });

  } catch (error) {
    console.error('Error obteniendo viajes disponibles:', error);
    res.status(500).json({
      message: 'Error al obtener viajes disponibles',
      error: error.message
    });
  }
});

// Ruta para aceptar un viaje (conductor)
app.post('/api/trips/:tripId/accept', async (req, res) => {
  try {
    const { tripId } = req.params;
    const { driverId } = req.body;

    const trip = await Trip.findById(tripId);
    if (!trip) {
      return res.status(404).json({ message: 'Viaje no encontrado' });
    }

    if (trip.status !== 'pending') {
      return res.status(400).json({ message: 'Este viaje ya no está disponible' });
    }

    trip.driver = driverId || '685233914eb58a9853a78d5f'; // Conductor de prueba
    trip.status = 'accepted';
    trip.acceptedAt = new Date();

    await trip.save();

    res.json({
      message: 'Viaje aceptado exitosamente',
      trip
    });

  } catch (error) {
    console.error('Error aceptando viaje:', error);
    res.status(500).json({
      message: 'Error al aceptar viaje',
      error: error.message
    });
  }
});

// Ruta para iniciar un viaje
app.post('/api/trips/:tripId/start', async (req, res) => {
  try {
    const { tripId } = req.params;

    const trip = await Trip.findById(tripId);
    if (!trip) {
      return res.status(404).json({ message: 'Viaje no encontrado' });
    }

    if (trip.status !== 'accepted') {
      return res.status(400).json({ message: 'El viaje debe estar aceptado para iniciarse' });
    }

    trip.status = 'in_progress';
    trip.startedAt = new Date();

    await trip.save();

    res.json({
      message: 'Viaje iniciado exitosamente',
      trip
    });

  } catch (error) {
    console.error('Error iniciando viaje:', error);
    res.status(500).json({
      message: 'Error al iniciar viaje',
      error: error.message
    });
  }
});

// Ruta para completar un viaje
app.post('/api/trips/:tripId/complete', async (req, res) => {
  try {
    const { tripId } = req.params;

    const trip = await Trip.findById(tripId);
    if (!trip) {
      return res.status(404).json({ message: 'Viaje no encontrado' });
    }

    if (trip.status !== 'in_progress') {
      return res.status(400).json({ message: 'El viaje debe estar en progreso para completarse' });
    }

    trip.status = 'completed';
    trip.completedAt = new Date();

    // Calcular comisión (10%)
    const commission = trip.offeredPrice * 0.10;
    trip.commission = commission;

    await trip.save();

    res.json({
      message: 'Viaje completado exitosamente',
      trip,
      commission
    });

  } catch (error) {
    console.error('Error completando viaje:', error);
    res.status(500).json({
      message: 'Error al completar viaje',
      error: error.message
    });
  }
});

// Ruta para actualizar ubicación del conductor
app.post('/api/trips/:tripId/location', async (req, res) => {
  try {
    const { tripId } = req.params;
    const { latitude, longitude } = req.body;

    if (!latitude || !longitude) {
      return res.status(400).json({
        message: 'Latitud y longitud son requeridas'
      });
    }

    const trip = await Trip.findById(tripId);
    if (!trip) {
      return res.status(404).json({ message: 'Viaje no encontrado' });
    }

    // Actualizar ubicación del conductor en el viaje
    trip.driverLocation = {
      latitude,
      longitude,
      updatedAt: new Date()
    };

    await trip.save();

    res.json({
      message: 'Ubicación actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error actualizando ubicación:', error);
    res.status(500).json({
      message: 'Error al actualizar ubicación',
      error: error.message
    });
  }
});

// Ruta para obtener estadísticas
app.get('/api/stats', async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const totalDrivers = await User.countDocuments({ userType: 'driver' });
    const totalPassengers = await User.countDocuments({ userType: 'passenger' });
    const totalTrips = await Trip.countDocuments();

    res.json({
      totalUsers,
      totalDrivers,
      totalPassengers,
      totalTrips,
      message: 'Estadísticas obtenidas correctamente'
    });
  } catch (error) {
    res.status(500).json({ message: 'Error al obtener estadísticas', error: error.message });
  }
});

// Ruta catch-all para debugging
app.use('/api/*', (req, res) => {
  console.log(`Ruta solicitada: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ 
    message: 'Ruta no encontrada',
    requestedRoute: req.originalUrl,
    method: req.method
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor Maclaren ejecutándose en puerto ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📊 Modo: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
