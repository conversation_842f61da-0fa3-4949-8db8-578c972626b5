const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const http = require('http');
const socketIo = require('socket.io');

// Configuración
dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Datos en memoria para pruebas
const users = [
  {
    id: '1',
    firstName: 'Juan',
    lastName: 'Pérez',
    email: '<EMAIL>',
    password: '123456', // En producción esto estaría hasheado
    phone: '+57 ************',
    userType: 'both',
    isActive: true,
    balance: 50,
    documentsVerified: true
  },
  {
    id: '2',
    firstName: 'María',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
    phone: '+57 ************',
    userType: 'passenger',
    isActive: true,
    balance: 25,
    documentsVerified: false
  }
];

// Hacer io disponible para las rutas
app.set('io', io);

// Ruta de prueba
app.get('/api/health', (req, res) => {
  res.json({ 
    message: 'Servidor Maclaren funcionando correctamente',
    timestamp: new Date().toISOString(),
    mode: 'simple'
  });
});

// Ruta de login simplificada
app.post('/api/auth/login', (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        message: 'Email y contraseña son requeridos'
      });
    }

    // Buscar usuario
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
      return res.status(401).json({
        message: 'Credenciales inválidas'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        message: 'Cuenta desactivada. Contacta al soporte'
      });
    }

    // Generar token simple (en producción usar JWT)
    const token = `simple_token_${user.id}_${Date.now()}`;

    res.json({
      message: 'Inicio de sesión exitoso',
      token,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        userType: user.userType,
        balance: user.balance,
        documentsVerified: user.documentsVerified
      }
    });

  } catch (error) {
    console.error('Error en login:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Ruta de registro simplificada
app.post('/api/auth/register', (req, res) => {
  try {
    const { firstName, lastName, email, password, phone, userType } = req.body;

    if (!firstName || !lastName || !email || !password || !phone) {
      return res.status(400).json({
        message: 'Todos los campos son requeridos'
      });
    }

    // Verificar si el usuario ya existe
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({
        message: 'Ya existe un usuario con este correo electrónico'
      });
    }

    // Crear nuevo usuario
    const newUser = {
      id: (users.length + 1).toString(),
      firstName,
      lastName,
      email,
      password,
      phone,
      userType: userType || 'passenger',
      isActive: true,
      balance: 0,
      documentsVerified: false
    };

    users.push(newUser);

    res.status(201).json({
      message: 'Usuario registrado exitosamente',
      user: {
        id: newUser.id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        phone: newUser.phone,
        userType: newUser.userType,
        balance: newUser.balance,
        documentsVerified: newUser.documentsVerified
      }
    });

  } catch (error) {
    console.error('Error en registro:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Rutas adicionales para compatibilidad
app.get('/api/users/profile', (req, res) => {
  res.json({
    message: 'Perfil de usuario (demo)',
    user: users[0]
  });
});

app.get('/api/trips/history/:userId', (req, res) => {
  res.json({
    trips: [],
    message: 'Historial de viajes (demo)'
  });
});

app.post('/api/payments/recharge', (req, res) => {
  res.json({
    message: 'Recarga simulada exitosa',
    balance: 100
  });
});

// Ruta catch-all para debugging
app.use('/api/*', (req, res) => {
  console.log(`Ruta solicitada: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    message: 'Ruta no encontrada',
    requestedRoute: req.originalUrl,
    method: req.method
  });
});

// Socket.IO para tiempo real
io.on('connection', (socket) => {
  console.log('Usuario conectado:', socket.id);

  socket.on('disconnect', () => {
    console.log('Usuario desconectado:', socket.id);
  });
});

// Middleware de manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: 'Error interno del servidor',
    error: process.env.NODE_ENV === 'development' ? err.message : {}
  });
});

// Middleware para rutas no encontradas
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Ruta no encontrada' });
});

// Iniciar servidor
server.listen(PORT, () => {
  console.log(`🚀 Servidor Maclaren (Simple) corriendo en puerto ${PORT}`);
  console.log(`📱 API disponible en http://localhost:${PORT}/api`);
  console.log(`🔗 Socket.IO disponible en http://localhost:${PORT}`);
  console.log(`🧪 Modo: PRUEBAS (datos en memoria)`);
  console.log(`👤 Usuario de prueba: <EMAIL> / 123456`);
});

module.exports = { app, io };
