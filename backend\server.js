const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const http = require('http');
const socketIo = require('socket.io');
const connectDB = require('./config/database');

// Importar rutas
const authRoutes = require('./routes/auth');
const tripRoutes = require('./routes/trips');
const paymentRoutes = require('./routes/payments');
const userRoutes = require('./routes/users');

// Configuración
dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Servir archivos estáticos (uploads)
app.use('/uploads', express.static('uploads'));

// Hacer io disponible para las rutas
app.set('io', io);

// Rutas
app.use('/api/auth', authRoutes);
app.use('/api/trips', tripRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/users', userRoutes);

// Ruta de prueba
app.get('/api/health', (req, res) => {
  res.json({ 
    message: 'Servidor Maclaren funcionando correctamente',
    timestamp: new Date().toISOString()
  });
});

// Socket.IO para tiempo real
io.on('connection', (socket) => {
  console.log('Usuario conectado:', socket.id);

  // Configurar sesión de usuario
  socket.on('user-session', (data) => {
    const { userId, userType } = data;
    socket.userId = userId;
    socket.userType = userType;
    console.log(`Usuario ${userId} (${userType}) configurado en socket ${socket.id}`);
  });

  // Unirse a una sala de viaje
  socket.on('join-trip', (tripId) => {
    socket.join(tripId);
    console.log(`Usuario ${socket.id} se unió al viaje ${tripId}`);
  });

  // Salir de una sala de viaje
  socket.on('leave-trip', (tripId) => {
    socket.leave(tripId);
    console.log(`Usuario ${socket.id} salió del viaje ${tripId}`);
  });

  // Solicitud de conductor (conductor quiere aceptar viaje)
  socket.on('driver-request', (data) => {
    const { tripId, driverInfo } = data;
    console.log(`Solicitud de conductor para viaje ${tripId}:`, driverInfo);

    // Enviar al pasajero en la sala del viaje
    socket.to(tripId).emit('driver-request-received', {
      tripId,
      driverInfo,
      timestamp: data.timestamp
    });
  });

  // Respuesta del pasajero (acepta/rechaza conductor)
  socket.on('passenger-response', (data) => {
    const { tripId, driverId, accepted } = data;
    console.log(`Respuesta del pasajero para viaje ${tripId}: ${accepted ? 'aceptado' : 'rechazado'}`);

    // Enviar al conductor específico
    socket.to(tripId).emit('passenger-response', {
      tripId,
      driverId,
      accepted,
      timestamp: data.timestamp
    });
  });

  // Actualizar ubicación del conductor
  socket.on('update-driver-location', (data) => {
    const { tripId, location } = data;
    socket.to(tripId).emit('driver-location-updated', {
      location,
      timestamp: data.timestamp
    });
  });

  // Cambio de estado del viaje
  socket.on('trip-status-change', (data) => {
    const { tripId, status, data: tripData } = data;
    console.log(`Estado del viaje ${tripId} cambió a: ${status}`);

    socket.to(tripId).emit('trip-status-updated', {
      tripId,
      status,
      tripData,
      timestamp: data.timestamp
    });
  });

  // Notificar nuevo viaje disponible
  socket.on('new-trip-request', (tripData) => {
    socket.broadcast.emit('trip-available', tripData);
  });

  // Notificar que un viaje fue aceptado
  socket.on('trip-accepted', (data) => {
    const { tripId, driverInfo } = data;
    socket.to(tripId).emit('trip-accepted', driverInfo);
  });

  // Notificar que un viaje fue completado
  socket.on('trip-completed', (tripId) => {
    socket.to(tripId).emit('trip-completed');
  });

  // Desconexión
  socket.on('disconnect', () => {
    console.log('Usuario desconectado:', socket.id);
  });
});

// Middleware de manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: 'Error interno del servidor',
    error: process.env.NODE_ENV === 'development' ? err.message : {}
  });
});

// Middleware para rutas no encontradas
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Ruta no encontrada' });
});

// Iniciar servidor
const startServer = async () => {
  try {
    await connectDB();

    server.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 Servidor Maclaren corriendo en puerto ${PORT}`);
      console.log(`📱 API disponible en http://localhost:${PORT}/api`);
      console.log(`🔗 Socket.IO disponible en http://localhost:${PORT}`);
      console.log(`🗄️  Base de datos: MongoDB`);
      console.log(`📋 Documentos: ${process.cwd()}/uploads/documents`);
      console.log(`🌐 Servidor escuchando en todas las interfaces (0.0.0.0:${PORT})`);
    });
  } catch (error) {
    console.error('❌ Error iniciando servidor:', error);
    process.exit(1);
  }
};

startServer();

module.exports = { app, io };
