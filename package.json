{"name": "maclaren", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/native": "^7.1.11", "@react-navigation/stack": "^7.3.4", "axios": "^1.10.0", "cors": "^2.8.5", "expo": "~53.0.11", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "~18.1.5", "expo-status-bar": "~2.2.3", "express": "^5.1.0", "firebase": "^11.9.1", "react": "19.0.0", "react-native": "0.79.3", "react-native-google-places-autocomplete": "^2.5.7", "react-native-maps": "1.20.1", "react-native-maps-directions": "^1.9.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}