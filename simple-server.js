const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// Configurar CORS para Socket.IO
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Almacenamiento en memoria
let activeTrips = new Map();
let driverRequests = new Map();
let connectedUsers = new Map();

// Ruta de salud
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Servidor Maclaren Simple funcionando',
    activeTrips: activeTrips.size,
    driverRequests: driverRequests.size,
    connectedUsers: connectedUsers.size
  });
});

// Rutas REST para viajes
app.post('/api/trips', (req, res) => {
  const trip = {
    id: `trip-${Date.now()}`,
    ...req.body,
    status: 'pending',
    createdAt: new Date().toISOString()
  };
  
  activeTrips.set(trip.id, trip);
  
  // Notificar a todos los conductores conectados
  io.emit('new-trip', trip);
  
  console.log('🚗 Nuevo viaje creado:', trip.id);
  res.json({ success: true, trip });
});

app.get('/api/trips', (req, res) => {
  const trips = Array.from(activeTrips.values()).filter(trip => trip.status === 'pending');
  res.json({ success: true, trips });
});

// Socket.IO para tiempo real
io.on('connection', (socket) => {
  console.log('👤 Usuario conectado:', socket.id);
  
  // Registrar usuario
  socket.on('register-user', (userData) => {
    connectedUsers.set(socket.id, userData);
    console.log('📝 Usuario registrado:', userData.userType, userData.userId);
  });
  
  // Unirse a sala de viaje
  socket.on('join-trip', (tripId) => {
    socket.join(`trip-${tripId}`);
    console.log('🏠 Usuario se unió a sala del viaje:', tripId);
  });
  
  // Solicitud de conductor
  socket.on('driver-request', (data) => {
    console.log('🚗 Solicitud de conductor recibida:', data);
    
    const request = {
      id: `request-${Date.now()}`,
      ...data,
      socketId: socket.id,
      timestamp: new Date().toISOString()
    };
    
    // Guardar solicitud
    if (!driverRequests.has(data.tripId)) {
      driverRequests.set(data.tripId, []);
    }
    driverRequests.get(data.tripId).push(request);
    
    // Enviar al pasajero en la sala del viaje
    socket.to(`trip-${data.tripId}`).emit('driver-request-received', request);
    
    console.log('📤 Solicitud enviada al pasajero del viaje:', data.tripId);
  });
  
  // Respuesta del pasajero
  socket.on('passenger-response', (data) => {
    console.log('👤 Respuesta del pasajero recibida:', data);
    
    // Encontrar la solicitud original
    const tripRequests = driverRequests.get(data.tripId) || [];
    const originalRequest = tripRequests.find(req => req.driverInfo.id === data.driverId);
    
    if (originalRequest) {
      // Enviar respuesta al conductor específico
      io.to(originalRequest.socketId).emit('passenger-response', {
        ...data,
        originalRequest: originalRequest
      });
      
      console.log('📤 Respuesta enviada al conductor:', data.driverId);
      
      if (data.accepted) {
        // Limpiar solicitudes del viaje
        driverRequests.delete(data.tripId);
        
        // Actualizar estado del viaje
        const trip = activeTrips.get(data.tripId);
        if (trip) {
          trip.status = 'accepted';
          trip.driver = originalRequest.driverInfo;
          activeTrips.set(data.tripId, trip);
        }
        
        console.log('✅ Viaje aceptado y actualizado:', data.tripId);
      }
    } else {
      console.log('❌ No se encontró la solicitud original');
    }
  });
  
  // Desconexión
  socket.on('disconnect', () => {
    const userData = connectedUsers.get(socket.id);
    if (userData) {
      console.log('👋 Usuario desconectado:', userData.userType, userData.userId);
      connectedUsers.delete(socket.id);
    } else {
      console.log('👋 Usuario desconectado:', socket.id);
    }
  });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 Servidor Maclaren Simple corriendo en puerto', PORT);
  console.log('📱 API disponible en http://localhost:' + PORT);
  console.log('🔗 Socket.IO disponible en http://localhost:' + PORT);
  console.log('🌐 Servidor escuchando en todas las interfaces');
});

// Limpiar datos antiguos cada 30 minutos
setInterval(() => {
  const now = Date.now();
  const thirtyMinutes = 30 * 60 * 1000;
  
  // Limpiar viajes antiguos
  for (const [tripId, trip] of activeTrips.entries()) {
    if (now - new Date(trip.createdAt).getTime() > thirtyMinutes) {
      activeTrips.delete(tripId);
      driverRequests.delete(tripId);
    }
  }
  
  console.log('🧹 Limpieza automática completada');
}, 30 * 60 * 1000);
