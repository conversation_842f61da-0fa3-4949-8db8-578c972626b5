import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

const GradientButton = ({
  title,
  onPress,
  colors = ['#ff6b6b', '#ee5a24'],
  style,
  textStyle,
  disabled = false,
  loading = false,
  icon,
  iconSize = 20,
  ...props
}) => {
  const buttonColors = disabled ? ['#ccc', '#999'] : colors;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      <LinearGradient
        colors={buttonColors}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.content}>
          {icon && !loading && (
            <Ionicons 
              name={icon} 
              size={iconSize} 
              color="white" 
              style={styles.icon} 
            />
          )}
          {loading && (
            <Ionicons 
              name="refresh" 
              size={iconSize} 
              color="white" 
              style={[styles.icon, styles.spinning]} 
            />
          )}
          <Text style={[styles.text, textStyle]}>
            {loading ? 'Cargando...' : title}
          </Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  gradient: {
    paddingVertical: 14,
    paddingHorizontal: 20,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  icon: {
    marginRight: 8,
  },
  spinning: {
    // Aquí podrías agregar una animación de rotación si quisieras
  },
});

export default GradientButton;
