import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions, Text } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import { Card, Button, Surface, Chip } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

const NavigationMap = ({ 
  trip, 
  userMode, 
  onArriveAtPickup, 
  onArriveAtDestination,
  onUpdateLocation 
}) => {
  const [currentLocation, setCurrentLocation] = useState(null);
  const [route, setRoute] = useState([]);
  const [distance, setDistance] = useState(0);
  const [duration, setDuration] = useState(0);
  const [navigationPhase, setNavigationPhase] = useState('to_pickup'); // 'to_pickup' o 'to_destination'
  const mapRef = useRef(null);

  useEffect(() => {
    getCurrentLocation();
    const interval = setInterval(getCurrentLocation, 5000); // Actualizar cada 5 segundos
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (currentLocation && trip) {
      calculateRoute();
      if (onUpdateLocation) {
        onUpdateLocation(currentLocation);
      }
    }
  }, [currentLocation, navigationPhase]);

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };
      
      setCurrentLocation(newLocation);
      checkArrival(newLocation);
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const calculateRoute = () => {
    if (!currentLocation || !trip) return;

    const destination = navigationPhase === 'to_pickup' 
      ? trip.pickupLocation 
      : trip.destination;

    // Simular ruta (en producción usarías Google Directions API)
    const routePoints = [
      currentLocation,
      {
        latitude: (currentLocation.latitude + destination.latitude) / 2,
        longitude: (currentLocation.longitude + destination.longitude) / 2,
      },
      destination
    ];

    setRoute(routePoints);

    // Calcular distancia aproximada
    const dist = calculateDistance(currentLocation, destination);
    setDistance(dist);
    setDuration(Math.round(dist * 2)); // Aproximadamente 2 minutos por km
  };

  const calculateDistance = (point1, point2) => {
    const R = 6371; // Radio de la Tierra en km
    const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
    const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const checkArrival = (location) => {
    if (!trip) return;

    const destination = navigationPhase === 'to_pickup' 
      ? trip.pickupLocation 
      : trip.destination;

    const dist = calculateDistance(location, destination);
    
    // Si está a menos de 100 metros
    if (dist < 0.1) {
      if (navigationPhase === 'to_pickup') {
        setNavigationPhase('to_destination');
        if (onArriveAtPickup) onArriveAtPickup();
      } else {
        if (onArriveAtDestination) onArriveAtDestination();
      }
    }
  };

  const getDestinationInfo = () => {
    if (!trip) return {};
    
    if (navigationPhase === 'to_pickup') {
      return {
        title: '📍 Recogiendo pasajero',
        address: trip.pickupLocation.address || 'Punto de recogida',
        icon: 'person',
        color: '#ff6b6b'
      };
    } else {
      return {
        title: '🎯 Llevando al destino',
        address: trip.destination.address || 'Destino final',
        icon: 'flag',
        color: '#4CAF50'
      };
    }
  };

  if (!currentLocation || !trip) {
    return (
      <View style={styles.loadingContainer}>
        <Text>🗺️ Cargando navegación...</Text>
      </View>
    );
  }

  const destinationInfo = getDestinationInfo();
  const targetLocation = navigationPhase === 'to_pickup' 
    ? trip.pickupLocation 
    : trip.destination;

  return (
    <View style={styles.container}>
      {/* Información de navegación */}
      <Surface style={styles.navigationInfo} elevation={4}>
        <LinearGradient
          colors={[destinationInfo.color, destinationInfo.color + '80']}
          style={styles.infoGradient}
        >
          <View style={styles.infoContent}>
            <Ionicons name={destinationInfo.icon} size={24} color="white" />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{destinationInfo.title}</Text>
              <Text style={styles.infoAddress}>{destinationInfo.address}</Text>
            </View>
            <View style={styles.infoStats}>
              <Text style={styles.statText}>{distance.toFixed(1)} km</Text>
              <Text style={styles.statText}>{duration} min</Text>
            </View>
          </View>
        </LinearGradient>
      </Surface>

      {/* Mapa de navegación */}
      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={{
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        showsUserLocation={true}
        followsUserLocation={true}
        showsMyLocationButton={false}
        showsTraffic={true}
      >
        {/* Marcador del destino actual */}
        <Marker
          coordinate={targetLocation}
          title={destinationInfo.title}
          description={destinationInfo.address}
          pinColor={destinationInfo.color}
        >
          <View style={[styles.customMarker, { backgroundColor: destinationInfo.color }]}>
            <Ionicons name={destinationInfo.icon} size={20} color="white" />
          </View>
        </Marker>

        {/* Ruta */}
        {route.length > 0 && (
          <Polyline
            coordinates={route}
            strokeColor={destinationInfo.color}
            strokeWidth={4}
            strokePattern={[1]}
          />
        )}

        {/* Mostrar destino final si estamos yendo al pickup */}
        {navigationPhase === 'to_pickup' && (
          <Marker
            coordinate={trip.destination}
            title="🎯 Destino final"
            description={trip.destination.address || 'Destino'}
            pinColor="#4CAF50"
            opacity={0.6}
          />
        )}
      </MapView>

      {/* Botones de acción */}
      <View style={styles.actionButtons}>
        {navigationPhase === 'to_pickup' && (
          <Button
            mode="contained"
            onPress={() => {
              setNavigationPhase('to_destination');
              if (onArriveAtPickup) onArriveAtPickup();
            }}
            style={styles.actionButton}
            buttonColor="#ff6b6b"
            icon="check"
          >
            He llegado al pasajero ✅
          </Button>
        )}

        {navigationPhase === 'to_destination' && (
          <Button
            mode="contained"
            onPress={() => {
              if (onArriveAtDestination) onArriveAtDestination();
            }}
            style={styles.actionButton}
            buttonColor="#4CAF50"
            icon="flag"
          >
            Viaje completado 🎉
          </Button>
        )}

        <Button
          mode="outlined"
          onPress={() => {
            if (mapRef.current) {
              mapRef.current.animateToRegion({
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }, 1000);
            }
          }}
          style={styles.actionButton}
          textColor={destinationInfo.color}
          icon="crosshairs-gps"
        >
          Centrar en mi ubicación
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  navigationInfo: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  infoGradient: {
    padding: 16,
  },
  infoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoAddress: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  infoStats: {
    alignItems: 'flex-end',
  },
  statText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  map: {
    flex: 1,
  },
  customMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    gap: 10,
  },
  actionButton: {
    borderRadius: 12,
  },
});

export default NavigationMap;
