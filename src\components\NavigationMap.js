import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions, Text } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import { Card, Button, Surface, Chip } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

const NavigationMap = ({ 
  trip, 
  userMode, 
  onArriveAtPickup, 
  onArriveAtDestination,
  onUpdateLocation 
}) => {
  const [currentLocation, setCurrentLocation] = useState(null);
  const [route, setRoute] = useState([]);
  const [distance, setDistance] = useState(0);
  const [duration, setDuration] = useState(0);
  const [navigationPhase, setNavigationPhase] = useState('to_pickup'); // 'to_pickup' o 'to_destination'
  const mapRef = useRef(null);

  useEffect(() => {
    getCurrentLocation();
    const interval = setInterval(getCurrentLocation, 5000); // Actualizar cada 5 segundos
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (currentLocation && trip) {
      calculateRoute();
      if (onUpdateLocation) {
        onUpdateLocation(currentLocation);
      }
    }
  }, [currentLocation, navigationPhase]);

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };
      
      setCurrentLocation(newLocation);
      checkArrival(newLocation);
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const calculateRoute = async () => {
    if (!currentLocation || !trip) return;

    const destination = navigationPhase === 'to_pickup'
      ? trip.pickupLocation
      : trip.destination;

    // Verificar que el destino tenga coordenadas válidas
    if (!destination || !destination.latitude || !destination.longitude) {
      console.warn('⚠️ Destino sin coordenadas válidas:', destination);
      return;
    }

    try {
      // Usar Google Directions API para obtener ruta real
      const apiKey = 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8';
      const origin = `${currentLocation.latitude},${currentLocation.longitude}`;
      const dest = `${destination.latitude},${destination.longitude}`;

      const directionsUrl = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${dest}&key=${apiKey}&mode=driving&language=es&region=co`;

      console.log('🗺️ Calculando ruta real con Google Directions API...');

      const response = await fetch(directionsUrl);
      const data = await response.json();

      if (data.status === 'OK' && data.routes.length > 0) {
        const route = data.routes[0];
        const leg = route.legs[0];

        // Decodificar polyline para obtener puntos de la ruta
        const routePoints = decodePolyline(route.overview_polyline.points);
        setRoute(routePoints);

        // Usar datos reales de distancia y duración
        setDistance(leg.distance.value / 1000); // Convertir metros a km
        setDuration(Math.round(leg.duration.value / 60)); // Convertir segundos a minutos

        console.log('✅ Ruta real calculada:', {
          distance: leg.distance.text,
          duration: leg.duration.text,
          points: routePoints.length
        });
      } else {
        console.warn('⚠️ No se pudo obtener ruta, usando cálculo directo');
        // Fallback: línea directa
        const routePoints = [currentLocation, destination];
        setRoute(routePoints);

        const dist = calculateDistance(currentLocation, destination);
        setDistance(dist);
        setDuration(Math.round(dist * 2));
      }
    } catch (error) {
      console.error('❌ Error calculando ruta:', error);
      // Fallback: línea directa
      const routePoints = [currentLocation, destination];
      setRoute(routePoints);

      const dist = calculateDistance(currentLocation, destination);
      setDistance(dist);
      setDuration(Math.round(dist * 2));
    }
  };

  const calculateDistance = (point1, point2) => {
    const R = 6371; // Radio de la Tierra en km
    const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
    const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Función para decodificar polylines de Google Maps
  const decodePolyline = (encoded) => {
    const points = [];
    let index = 0;
    const len = encoded.length;
    let lat = 0;
    let lng = 0;

    while (index < len) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charAt(index++).charCodeAt(0) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charAt(index++).charCodeAt(0) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return points;
  };

  const checkArrival = (location) => {
    if (!trip) return;

    const destination = navigationPhase === 'to_pickup'
      ? trip.pickupLocation
      : trip.destination;

    // Verificar que el destino tenga coordenadas válidas
    if (!destination || !destination.latitude || !destination.longitude) {
      console.warn('⚠️ Destino sin coordenadas válidas para verificar llegada:', destination);
      return;
    }

    const dist = calculateDistance(location, destination);

    // Si está a menos de 100 metros
    if (dist < 0.1) {
      if (navigationPhase === 'to_pickup') {
        setNavigationPhase('to_destination');
        if (onArriveAtPickup) onArriveAtPickup();
      } else {
        if (onArriveAtDestination) onArriveAtDestination();
      }
    }
  };

  const getDestinationInfo = () => {
    if (!trip) return {
      title: 'Cargando...',
      address: 'Obteniendo información',
      icon: 'hourglass',
      color: '#666'
    };

    if (navigationPhase === 'to_pickup') {
      return {
        title: '📍 Recogiendo pasajero',
        address: (trip.pickupLocation && trip.pickupLocation.address) || 'Punto de recogida',
        icon: 'person',
        color: '#ff6b6b'
      };
    } else {
      return {
        title: '🎯 Llevando al destino',
        address: (trip.destination && trip.destination.address) || 'Destino final',
        icon: 'flag',
        color: '#4CAF50'
      };
    }
  };

  if (!currentLocation || !trip) {
    return (
      <View style={styles.loadingContainer}>
        <Text>🗺️ Cargando navegación...</Text>
      </View>
    );
  }

  const destinationInfo = getDestinationInfo();
  const targetLocation = navigationPhase === 'to_pickup'
    ? trip.pickupLocation
    : trip.destination;

  // Verificar que targetLocation tenga coordenadas válidas
  if (!targetLocation || !targetLocation.latitude || !targetLocation.longitude) {
    return (
      <View style={styles.loadingContainer}>
        <Text>⚠️ Error: Ubicación de destino no válida</Text>
        <Text>Verifica que el viaje tenga coordenadas correctas</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Información de navegación */}
      <Surface style={styles.navigationInfo} elevation={4}>
        <LinearGradient
          colors={[destinationInfo.color, destinationInfo.color + '80']}
          style={styles.infoGradient}
        >
          <View style={styles.infoContent}>
            <Ionicons name={destinationInfo.icon} size={24} color="white" />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{destinationInfo.title}</Text>
              <Text style={styles.infoAddress}>{destinationInfo.address}</Text>
            </View>
            <View style={styles.infoStats}>
              <Text style={styles.statText}>{distance.toFixed(1)} km</Text>
              <Text style={styles.statText}>{duration} min</Text>
            </View>
          </View>
        </LinearGradient>
      </Surface>

      {/* Mapa de navegación */}
      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={{
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        showsUserLocation={true}
        followsUserLocation={true}
        showsMyLocationButton={false}
        showsTraffic={true}
      >
        {/* Marcador del destino actual */}
        <Marker
          coordinate={targetLocation}
          title={destinationInfo.title}
          description={destinationInfo.address}
          pinColor={destinationInfo.color}
        >
          <View style={[styles.customMarker, { backgroundColor: destinationInfo.color }]}>
            <Ionicons name={destinationInfo.icon} size={20} color="white" />
          </View>
        </Marker>

        {/* Ruta real de navegación */}
        {route.length > 0 && (
          <>
            {/* Línea de fondo (más gruesa y oscura) */}
            <Polyline
              coordinates={route}
              strokeColor="#000000"
              strokeWidth={8}
              strokeOpacity={0.3}
            />
            {/* Línea principal de la ruta */}
            <Polyline
              coordinates={route}
              strokeColor={destinationInfo.color}
              strokeWidth={5}
              strokeOpacity={0.9}
            />
          </>
        )}

        {/* Mostrar destino final si estamos yendo al pickup */}
        {navigationPhase === 'to_pickup' && trip.destination && trip.destination.latitude && trip.destination.longitude && (
          <Marker
            coordinate={trip.destination}
            title="🎯 Destino final"
            description={trip.destination.address || 'Destino'}
            pinColor="#4CAF50"
            opacity={0.6}
          />
        )}
      </MapView>

      {/* Botones de acción */}
      <View style={styles.actionButtons}>
        {navigationPhase === 'to_pickup' && (
          <Button
            mode="contained"
            onPress={() => {
              setNavigationPhase('to_destination');
              if (onArriveAtPickup) onArriveAtPickup();
            }}
            style={styles.actionButton}
            buttonColor="#ff6b6b"
            icon="check"
          >
            He llegado al pasajero ✅
          </Button>
        )}

        {navigationPhase === 'to_destination' && (
          <Button
            mode="contained"
            onPress={() => {
              if (onArriveAtDestination) onArriveAtDestination();
            }}
            style={styles.actionButton}
            buttonColor="#4CAF50"
            icon="flag"
          >
            Viaje completado 🎉
          </Button>
        )}

        <Button
          mode="outlined"
          onPress={() => {
            if (mapRef.current) {
              mapRef.current.animateToRegion({
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }, 1000);
            }
          }}
          style={styles.actionButton}
          textColor={destinationInfo.color}
          icon="crosshairs-gps"
        >
          Centrar en mi ubicación
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  navigationInfo: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  infoGradient: {
    padding: 16,
  },
  infoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoAddress: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  infoStats: {
    alignItems: 'flex-end',
  },
  statText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  map: {
    flex: 1,
  },
  customMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    gap: 10,
  },
  actionButton: {
    borderRadius: 12,
  },
});

export default NavigationMap;
