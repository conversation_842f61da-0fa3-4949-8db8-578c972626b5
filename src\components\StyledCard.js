import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Surface } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';

const StyledCard = ({
  children,
  style,
  gradient = false,
  gradientColors = ['#ffffff', '#f8f9fa'],
  elevation = 4,
  borderRadius = 16,
  padding = 20,
  ...props
}) => {
  if (gradient) {
    return (
      <Surface style={[styles.container, { borderRadius }, style]} elevation={elevation}>
        <LinearGradient
          colors={gradientColors}
          style={[styles.gradientContainer, { borderRadius, padding }]}
        >
          {children}
        </LinearGradient>
      </Surface>
    );
  }

  return (
    <Card 
      style={[styles.card, { borderRadius }, style]} 
      elevation={elevation}
      {...props}
    >
      <Card.Content style={[styles.content, { padding }]}>
        {children}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  gradientContainer: {
    overflow: 'hidden',
  },
  card: {
    backgroundColor: 'white',
  },
  content: {
    // El padding se maneja diná<PERSON>amente
  },
});

export default StyledCard;
