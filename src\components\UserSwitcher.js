import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Menu,
  Button,
  Divider,
  Avatar,
  Text,
} from 'react-native-paper';
import { useAuth } from '../context/AuthContext';

const UserSwitcher = ({ style }) => {
  const { user, switchUser } = useAuth();
  const [visible, setVisible] = useState(false);
  const [switching, setSwitching] = useState(false);

  const openMenu = () => setVisible(true);
  const closeMenu = () => setVisible(false);

  const handleSwitchUser = async (email, userName, userType) => {
    try {
      setSwitching(true);
      closeMenu();
      
      console.log(`🔄 Iniciando cambio a ${userName}`);
      
      const result = await switchUser(email);
      
      if (result.success) {
        Alert.alert(
          '✅ Cambio exitoso',
          `Ahora estás usando la cuenta de ${userName} (${userType})`
        );
      } else {
        Alert.alert('Error', result.error || 'Error al cambiar usuario');
      }
    } catch (error) {
      console.error('Error switching user:', error);
      Alert.alert('Error', 'Error inesperado al cambiar usuario');
    } finally {
      setSwitching(false);
    }
  };

  const getCurrentUserInfo = () => {
    if (!user) return { icon: 'account', color: '#666', label: 'Usuario' };
    
    if (user.userType === 'driver') {
      return {
        icon: 'car',
        color: '#2196F3',
        label: `🚗 ${user.firstName}`,
      };
    } else {
      return {
        icon: 'account',
        color: '#4CAF50',
        label: `👤 ${user.firstName}`,
      };
    }
  };

  const currentUser = getCurrentUserInfo();
  const isPassenger = user?.userType === 'passenger';
  const isDriver = user?.userType === 'driver';

  return (
    <View style={[styles.container, style]}>
      <Menu
        visible={visible}
        onDismiss={closeMenu}
        anchor={
          <Button
            mode="outlined"
            onPress={openMenu}
            icon={currentUser.icon}
            loading={switching}
            disabled={switching}
            style={[styles.switchButton, { borderColor: currentUser.color }]}
            labelStyle={{ color: currentUser.color }}
          >
            {switching ? 'Cambiando...' : currentUser.label}
          </Button>
        }
        contentStyle={styles.menuContent}
      >
        <View style={styles.menuHeader}>
          <Text style={styles.menuTitle}>Cambiar Usuario</Text>
        </View>
        
        <Divider />
        
        {/* Opción Pasajero */}
        <Menu.Item
          onPress={() => handleSwitchUser('<EMAIL>', 'Ana Pasajero', 'Pasajero')}
          title="👤 Ana Pasajero"
          description="Crear y gestionar viajes"
          leadingIcon="account"
          disabled={isPassenger || switching}
          style={isPassenger ? styles.currentUserItem : null}
        />
        
        {/* Opción Conductor */}
        <Menu.Item
          onPress={() => handleSwitchUser('<EMAIL>', 'Carlos Conductor', 'Conductor')}
          title="🚗 Carlos Conductor"
          description="Ver y aceptar viajes"
          leadingIcon="car"
          disabled={isDriver || switching}
          style={isDriver ? styles.currentUserItem : null}
        />
        
        <Divider />
        
        <View style={styles.menuFooter}>
          <Text style={styles.footerText}>
            💡 Cambio instantáneo sin cerrar la app
          </Text>
        </View>
      </Menu>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  switchButton: {
    minWidth: 150,
  },
  menuContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    elevation: 8,
    minWidth: 250,
  },
  menuHeader: {
    padding: 16,
    alignItems: 'center',
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a2e',
  },
  currentUserItem: {
    backgroundColor: '#f0f0f0',
  },
  menuFooter: {
    padding: 12,
    backgroundColor: '#f8f9fa',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default UserSwitcher;
