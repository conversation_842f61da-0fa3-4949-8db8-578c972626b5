import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Sistema de almacenamiento local para viajes
export const OfflineStorage = {
  // Obtener todos los viajes creados localmente
  async getCreatedTrips() {
    try {
      const trips = await AsyncStorage.getItem('offline_created_trips');
      return trips ? JSON.parse(trips) : [];
    } catch (error) {
      console.warn('Error al obtener viajes creados:', error);
      return [];
    }
  },

  // Guardar un nuevo viaje creado
  async saveCreatedTrip(trip) {
    try {
      const existingTrips = await this.getCreatedTrips();
      const updatedTrips = [...existingTrips, trip];
      await AsyncStorage.setItem('offline_created_trips', JSON.stringify(updatedTrips));
      console.log('✅ Viaje guardado localmente:', trip._id);
      return true;
    } catch (error) {
      console.warn('Error al guardar viaje:', error);
      return false;
    }
  },

  // Obtener viajes disponibles (base + creados localmente)
  async getAvailableTrips() {
    try {
      console.log('💾 Obteniendo viajes desde OfflineStorage...');
      const createdTrips = await this.getCreatedTrips();
      console.log('📝 Viajes creados localmente:', createdTrips.length);
      console.log('🏠 Viajes base del sistema:', OFFLINE_DATA.baseTrips.length);

      const allTrips = [...OFFLINE_DATA.baseTrips, ...createdTrips];
      console.log('📊 Total de viajes antes de filtrar:', allTrips.length);

      // Filtrar solo viajes pendientes
      const pendingTrips = allTrips.filter(trip => trip.status === 'pending');
      console.log('⏳ Viajes pendientes encontrados:', pendingTrips.length);

      return pendingTrips;
    } catch (error) {
      console.warn('❌ Error al obtener viajes disponibles:', error);
      console.log('🔄 Usando solo viajes base como fallback');
      return OFFLINE_DATA.baseTrips.filter(trip => trip.status === 'pending');
    }
  },

  // Actualizar estado de un viaje
  async updateTripStatus(tripId, status) {
    try {
      const createdTrips = await this.getCreatedTrips();
      const updatedTrips = createdTrips.map(trip =>
        trip._id === tripId ? { ...trip, status } : trip
      );
      await AsyncStorage.setItem('offline_created_trips', JSON.stringify(updatedTrips));
      console.log(`✅ Viaje ${tripId} actualizado a ${status}`);
      return true;
    } catch (error) {
      console.warn('Error al actualizar viaje:', error);
      return false;
    }
  },

  // Limpiar todos los viajes (para testing)
  async clearAllTrips() {
    try {
      await AsyncStorage.removeItem('offline_created_trips');
      console.log('🗑️ Todos los viajes locales eliminados');
      return true;
    } catch (error) {
      console.warn('Error al limpiar viajes:', error);
      return false;
    }
  }
};

// Configuración de la API
export const API_CONFIG = {
  // Cambia esta URL por la de tu backend cuando esté disponible
  BASE_URL: 'http://localhost:3000/api',
  SOCKET_URL: 'http://localhost:3000',
  TIMEOUT: 5000, // Reducido a 5 segundos para fallar más rápido
  RETRY_ATTEMPTS: 2,
  OFFLINE_MODE: false, // Deshabilitado para usar conexión real
};

// Crear instancia de axios con configuración mejorada
export const createApiInstance = () => {
  const api = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Interceptor para agregar token de autenticación
  api.interceptors.request.use(
    async (config) => {
      try {
        const token = await AsyncStorage.getItem('userToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('Error al obtener token:', error);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Interceptor para manejar respuestas y errores
  api.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      console.warn('API Error:', error.message);
      
      // Si es un error de red o timeout, activar modo offline
      if (error.code === 'ECONNABORTED' || error.code === 'NETWORK_ERROR' || !error.response) {
        console.log('🔄 Activando modo offline debido a error de conexión');
      }
      
      return Promise.reject(error);
    }
  );

  return api;
};

// Función helper para manejar peticiones con fallback offline
export const apiRequest = async (requestFn, fallbackData = null, errorMessage = 'Error de conexión') => {
  try {
    const result = await requestFn();
    return {
      success: true,
      data: result.data,
      isOffline: false,
    };
  } catch (error) {
    console.warn(`${errorMessage}:`, error.message);
    
    if (API_CONFIG.OFFLINE_MODE && fallbackData !== null) {
      console.log('📱 Usando datos offline');
      return {
        success: true,
        data: fallbackData,
        isOffline: true,
      };
    }
    
    return {
      success: false,
      error: error.response?.data?.message || errorMessage,
      isOffline: false,
    };
  }
};

// Función para verificar conectividad
export const checkApiConnectivity = async () => {
  try {
    const api = createApiInstance();
    await api.get('/health', { timeout: 3000 });
    return true;
  } catch (error) {
    console.log('🔌 API no disponible, usando modo offline');
    return false;
  }
};

// Datos de respaldo para modo offline
export const OFFLINE_DATA = {
  // Usuario pasajero
  passengerUser: {
    id: 'offline-passenger-123',
    firstName: 'Ana',
    lastName: 'Pasajero',
    email: '<EMAIL>',
    phone: '+57 ************',
    userType: 'passenger',
    balance: 50000,
    documentsVerified: true,
  },

  // Usuario conductor
  driverUser: {
    id: 'offline-driver-456',
    firstName: 'Carlos',
    lastName: 'Conductor',
    email: '<EMAIL>',
    phone: '+57 ************',
    userType: 'driver',
    balance: 75000,
    documentsVerified: true,
    vehicleInfo: {
      plate: 'ABC-123',
      model: 'Toyota Corolla 2020',
      color: 'Blanco'
    }
  },
  
  // Viajes base del sistema (siempre disponibles)
  baseTrips: [
    {
      _id: 'offline-base-trip-1',
      pickupLocation: {
        latitude: 4.6097,
        longitude: -74.0817,
        address: 'Centro de Bogotá, Bogotá'
      },
      destination: {
        latitude: 4.6351,
        longitude: -74.0703,
        address: 'Zona Rosa, Bogotá'
      },
      offeredPrice: 15000,
      status: 'pending',
      passenger: {
        id: 'offline-passenger-base-1',
        firstName: 'Juan',
        lastName: 'Pérez',
        phone: '+57 ************'
      },
      createdAt: new Date(Date.now() - 600000).toISOString()
    },
    {
      _id: 'offline-base-trip-2',
      pickupLocation: {
        latitude: 4.6482,
        longitude: -74.0776,
        address: 'Chapinero, Bogotá'
      },
      destination: {
        latitude: 4.7110,
        longitude: -74.0721,
        address: 'Suba, Bogotá'
      },
      offeredPrice: 20000,
      status: 'pending',
      passenger: {
        id: 'offline-passenger-base-2',
        firstName: 'María',
        lastName: 'González',
        phone: '+57 ************'
      },
      createdAt: new Date(Date.now() - 300000).toISOString()
    }
  ],
  
  tripHistory: [
    {
      _id: 'offline-history-1',
      pickupLocation: { address: 'Centro de Bogotá' },
      destination: { address: 'Zona Rosa' },
      offeredPrice: 15000,
      status: 'completed',
      createdAt: new Date(Date.now() - 86400000).toISOString(),
      completedAt: new Date(Date.now() - 86400000 + 1800000).toISOString()
    },
    {
      _id: 'offline-history-2',
      pickupLocation: { address: 'Chapinero' },
      destination: { address: 'Suba' },
      offeredPrice: 20000,
      status: 'completed',
      createdAt: new Date(Date.now() - 172800000).toISOString(),
      completedAt: new Date(Date.now() - 172800000 + 2400000).toISOString()
    }
  ],
  
  transactions: [
    {
      id: 'offline-txn-1',
      type: 'recharge',
      amount: 50000,
      date: new Date(Date.now() - 86400000).toISOString(),
      description: 'Recarga inicial (offline)'
    },
    {
      id: 'offline-txn-2',
      type: 'trip',
      amount: -15000,
      date: new Date(Date.now() - 43200000).toISOString(),
      description: 'Viaje completado (offline)'
    }
  ]
};
