// Configuración de Google Maps
export const GOOGLE_MAPS_CONFIG = {
  apiKey: 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8',
  
  // Configuración por defecto para las consultas
  defaultQuery: {
    key: 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8',
    language: 'es',
    components: 'country:co', // Restringir a Colombia
  },
  
  // Configuración para Google Places
  placesConfig: {
    key: 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8',
    language: 'es',
    types: 'establishment',
    components: 'country:co',
  },
  
  // Configuración para geocodificación
  geocodingConfig: {
    key: 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8',
    language: 'es',
    region: 'co',
  },
  
  // Configuración para direcciones
  directionsConfig: {
    key: 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8',
    language: 'es',
    region: 'co',
    mode: 'driving',
    avoid: 'tolls',
  },
  
  // Configuración por defecto para Bogotá
  defaultLocation: {
    latitude: 4.6097,
    longitude: -74.0817,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  },
  
  // Configuración del mapa
  mapConfig: {
    showsUserLocation: true,
    showsMyLocationButton: false,
    showsTraffic: true,
    showsBuildings: true,
    showsCompass: false,
    showsScale: false,
    mapType: 'standard',
  }
};

// URLs de la API de Google Maps
export const GOOGLE_MAPS_URLS = {
  places: 'https://maps.googleapis.com/maps/api/place',
  geocoding: 'https://maps.googleapis.com/maps/api/geocode',
  directions: 'https://maps.googleapis.com/maps/api/directions',
};
