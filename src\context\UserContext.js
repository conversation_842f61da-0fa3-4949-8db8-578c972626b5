import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const UserContext = createContext();

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export const UserProvider = ({ children }) => {
  const [userMode, setUserMode] = useState('passenger'); // 'passenger' or 'driver'
  const [balance, setBalance] = useState(0);
  const [currentTrip, setCurrentTrip] = useState(null);
  const [location, setLocation] = useState(null);

  useEffect(() => {
    loadUserPreferences();

    // Escuchar cambios en userData para recargar preferencias
    const interval = setInterval(() => {
      loadUserPreferences();
    }, 1000); // Verificar cada segundo si hay cambios

    return () => clearInterval(interval);
  }, []);

  const loadUserPreferences = async () => {
    try {
      const savedMode = await AsyncStorage.getItem('userMode');
      const savedBalance = await AsyncStorage.getItem('userBalance');
      const userData = await AsyncStorage.getItem('userData');

      if (savedMode) {
        setUserMode(savedMode);
      }

      // Si hay datos de usuario, usar su balance, sino usar el guardado
      if (userData) {
        const user = JSON.parse(userData);
        if (user.balance !== undefined) {
          setBalance(user.balance);
        }
      } else if (savedBalance) {
        setBalance(parseFloat(savedBalance));
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const switchMode = async (mode) => {
    try {
      setUserMode(mode);
      await AsyncStorage.setItem('userMode', mode);
    } catch (error) {
      console.error('Error switching mode:', error);
    }
  };

  const updateBalance = async (newBalance) => {
    try {
      setBalance(newBalance);
      await AsyncStorage.setItem('userBalance', newBalance.toString());
    } catch (error) {
      console.error('Error updating balance:', error);
    }
  };

  const addBalance = async (amount) => {
    const newBalance = balance + amount;
    await updateBalance(newBalance);
  };

  const deductBalance = async (amount) => {
    const newBalance = Math.max(0, balance - amount);
    await updateBalance(newBalance);
    return newBalance;
  };

  const updateLocation = (newLocation) => {
    setLocation(newLocation);
  };

  const startTrip = (tripData) => {
    setCurrentTrip(tripData);
  };

  const endTrip = () => {
    setCurrentTrip(null);
  };

  const value = {
    userMode,
    balance,
    currentTrip,
    location,
    switchMode,
    updateBalance,
    addBalance,
    deductBalance,
    updateLocation,
    startTrip,
    endTrip,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};
