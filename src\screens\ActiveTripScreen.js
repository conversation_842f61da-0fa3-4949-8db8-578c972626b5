import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, BackHandler } from 'react-native';
import { Card, Text, Button, Surface, Chip, Divider } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useUser } from '../context/UserContext';
import { useAuth } from '../context/AuthContext';
import { tripService } from '../services/tripService';
import NavigationMap from '../components/NavigationMap';

const ActiveTripScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { userMode, currentTrip, updateTrip, completeTrip } = useUser();
  const [tripStatus, setTripStatus] = useState('accepted'); // 'accepted', 'in_progress', 'completed'

  useEffect(() => {
    // Prevenir que el usuario salga accidentalmente
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      Alert.alert(
        'Viaje en progreso',
        '¿Estás seguro de que quieres salir? El viaje seguirá activo.',
        [
          { text: 'Cancelar', style: 'cancel' },
          { text: 'Salir', onPress: () => navigation.goBack() }
        ]
      );
      return true;
    });

    return () => backHandler.remove();
  }, []);

  const handleArriveAtPickup = async () => {
    setTripStatus('in_progress');
    Alert.alert(
      'Pasajero recogido ✅',
      'Ahora dirígete al destino final',
      [{ text: 'Entendido', style: 'default' }]
    );

    // Actualizar estado del viaje en el backend
    try {
      await tripService.startTrip(currentTrip._id);
    } catch (error) {
      console.log('Error updating trip status:', error);
    }
  };

  const handleArriveAtDestination = async () => {
    Alert.alert(
      'Viaje completado 🎉',
      `¡Excelente! Has completado el viaje.\n\nPrecio: $${currentTrip.offeredPrice}\nComisión (10%): $${(currentTrip.offeredPrice * 0.1).toFixed(0)}`,
      [
        {
          text: 'Finalizar viaje',
          onPress: async () => {
            try {
              await tripService.completeTrip(currentTrip._id);
              completeTrip();
              navigation.navigate('Home');
            } catch (error) {
              console.log('Error completing trip:', error);
              completeTrip();
              navigation.navigate('Home');
            }
          }
        }
      ]
    );
  };

  const handleUpdateLocation = async (location) => {
    try {
      await tripService.updateDriverLocation(currentTrip._id, location);
    } catch (error) {
      console.log('Error updating location:', error);
    }
  };

  const handleCancelTrip = () => {
    Alert.alert(
      'Cancelar viaje',
      '¿Estás seguro de que quieres cancelar este viaje?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Sí, cancelar',
          style: 'destructive',
          onPress: async () => {
            try {
              await tripService.cancelTrip(currentTrip._id, 'Cancelado por el conductor');
              completeTrip();
              navigation.navigate('Home');
            } catch (error) {
              console.log('Error canceling trip:', error);
              completeTrip();
              navigation.navigate('Home');
            }
          }
        }
      ]
    );
  };

  if (!currentTrip) {
    return (
      <View style={styles.container}>
        <Text>No hay viaje activo</Text>
      </View>
    );
  }

  const getStatusInfo = () => {
    switch (tripStatus) {
      case 'accepted':
        return {
          title: '🚗 Dirigiéndose al pasajero',
          subtitle: 'Ve al punto de recogida',
          color: '#ff6b6b'
        };
      case 'in_progress':
        return {
          title: '🎯 Viaje en progreso',
          subtitle: 'Llevando al destino',
          color: '#4CAF50'
        };
      default:
        return {
          title: '✅ Viaje completado',
          subtitle: 'Viaje finalizado',
          color: '#2196F3'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <View style={styles.container}>
      {/* Header del viaje */}
      <Surface style={styles.headerContainer} elevation={4}>
        <LinearGradient
          colors={[statusInfo.color, statusInfo.color + '80']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerInfo}>
              <Text style={styles.headerTitle}>{statusInfo.title}</Text>
              <Text style={styles.headerSubtitle}>{statusInfo.subtitle}</Text>
            </View>
            <Chip
              style={styles.priceChip}
              textStyle={styles.priceChipText}
            >
              ${currentTrip.offeredPrice}
            </Chip>
          </View>
        </LinearGradient>
      </Surface>

      {/* Información del viaje - Solo mitad de pantalla */}
      <View style={styles.tripInfoContainer}>
        <Card style={styles.tripCard}>
          <Card.Content>
            <View style={styles.tripDetails}>
              <View style={styles.locationInfo}>
                <Ionicons name="radio-button-on" size={16} color="#ff6b6b" />
                <View style={styles.locationText}>
                  <Text style={styles.locationLabel}>Recogida</Text>
                  <Text style={styles.locationAddress}>
                    {currentTrip.pickupLocation.address || 'Punto de recogida'}
                  </Text>
                </View>
              </View>

              <View style={styles.routeLine} />

              <View style={styles.locationInfo}>
                <Ionicons name="location" size={16} color="#4CAF50" />
                <View style={styles.locationText}>
                  <Text style={styles.locationLabel}>Destino</Text>
                  <Text style={styles.locationAddress}>
                    {currentTrip.destination.address || 'Destino final'}
                  </Text>
                </View>
              </View>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.passengerInfo}>
              <Ionicons name="person-circle" size={24} color="#666" />
              <View style={styles.passengerText}>
                <Text style={styles.passengerName}>
                  {currentTrip.passenger?.firstName} {currentTrip.passenger?.lastName}
                </Text>
                <Text style={styles.passengerPhone}>
                  {currentTrip.passenger?.phone}
                </Text>
              </View>
              <Button
                mode="outlined"
                onPress={() => Alert.alert('Llamar', 'Función de llamada próximamente')}
                icon="phone"
                compact
              >
                Llamar
              </Button>
            </View>

            {currentTrip.passengerNotes && (
              <>
                <Divider style={styles.divider} />
                <View style={styles.notesSection}>
                  <Text style={styles.notesLabel}>📝 Notas del pasajero:</Text>
                  <Text style={styles.notesText}>{currentTrip.passengerNotes}</Text>
                </View>
              </>
            )}
          </Card.Content>
        </Card>

        {/* Botón de cancelar */}
        <Button
          mode="outlined"
          onPress={handleCancelTrip}
          style={styles.cancelButton}
          textColor="#f44336"
          icon="close"
        >
          Cancelar viaje
        </Button>
      </View>

      {/* Mapa de navegación - Mitad de pantalla */}
      <View style={styles.mapContainer}>
        <NavigationMap
          trip={currentTrip}
          userMode={userMode}
          onArriveAtPickup={handleArriveAtPickup}
          onArriveAtDestination={handleArriveAtDestination}
          onUpdateLocation={handleUpdateLocation}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  headerContainer: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  headerGradient: {
    padding: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  priceChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  priceChipText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  tripInfoContainer: {
    flex: 1,
    padding: 16,
  },
  tripCard: {
    borderRadius: 16,
    marginBottom: 16,
  },
  tripDetails: {
    marginBottom: 16,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  locationText: {
    marginLeft: 12,
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  locationAddress: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: '#ddd',
    marginLeft: 7,
    marginVertical: 4,
  },
  divider: {
    marginVertical: 16,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passengerText: {
    flex: 1,
    marginLeft: 12,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  passengerPhone: {
    fontSize: 14,
    color: '#666',
  },
  notesSection: {
    marginTop: 8,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  cancelButton: {
    borderRadius: 12,
    borderColor: '#f44336',
  },
  mapContainer: {
    flex: 1,
  },
});

export default ActiveTripScreen;
