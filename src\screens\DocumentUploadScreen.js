import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Image } from 'react-native';
import { Button, Text, Card, List, Divider } from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { MaterialIcons } from '@expo/vector-icons';
import { authService } from '../services/authService';

const DocumentUploadScreen = ({ navigation, route }) => {
  const { userData } = route.params;
  const [documents, setDocuments] = useState({
    cedula: null,
    reciboPago: null,
    // Documentos adicionales para conductores
    registroVehicular: null,
    soat: null,
    licenciaConducir: null,
    placaVehiculo: null,
  });
  const [loading, setLoading] = useState(false);

  const isDriver = userData.userType === 'driver';

  const pickDocument = async (documentType) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setDocuments(prev => ({
          ...prev,
          [documentType]: result.assets[0]
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo seleccionar el documento');
    }
  };

  const takePhoto = async (documentType) => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Error', 'Se necesita permiso para acceder a la cámara');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setDocuments(prev => ({
          ...prev,
          [documentType]: result.assets[0]
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo tomar la foto');
    }
  };

  const showDocumentOptions = (documentType, title) => {
    Alert.alert(
      title,
      'Selecciona una opción',
      [
        { text: 'Tomar foto', onPress: () => takePhoto(documentType) },
        { text: 'Seleccionar de galería', onPress: () => pickDocument(documentType) },
        { text: 'Cancelar', style: 'cancel' },
      ]
    );
  };

  const validateDocuments = () => {
    if (!documents.cedula || !documents.reciboPago) {
      Alert.alert('Error', 'Debes subir la cédula y un recibo de pago');
      return false;
    }

    if (isDriver) {
      if (!documents.registroVehicular || !documents.soat || !documents.licenciaConducir) {
        Alert.alert('Error', 'Los conductores deben subir todos los documentos del vehículo');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateDocuments()) return;

    setLoading(true);
    
    try {
      const formData = new FormData();
      
      // Agregar datos del usuario
      formData.append('userData', JSON.stringify(userData));
      
      // Agregar documentos
      Object.keys(documents).forEach(key => {
        if (documents[key]) {
          formData.append(key, {
            uri: documents[key].uri,
            type: 'image/jpeg',
            name: `${key}.jpg`,
          });
        }
      });

      const result = await authService.uploadDocument(formData);
      
      if (result.success) {
        Alert.alert(
          'Documentos Enviados',
          'Tus documentos han sido enviados para verificación. Te notificaremos cuando sean aprobados.',
          [
            {
              text: 'Continuar',
              onPress: () => navigation.navigate('Login')
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudieron enviar los documentos');
    } finally {
      setLoading(false);
    }
  };

  const DocumentItem = ({ title, documentKey, required = true }) => (
    <List.Item
      title={title}
      description={required ? 'Requerido' : 'Opcional'}
      left={() => (
        <MaterialIcons 
          name={documents[documentKey] ? 'check-circle' : 'radio-button-unchecked'} 
          size={24} 
          color={documents[documentKey] ? '#4CAF50' : '#ccc'} 
        />
      )}
      right={() => (
        <Button 
          mode="outlined" 
          compact
          onPress={() => showDocumentOptions(documentKey, title)}
        >
          {documents[documentKey] ? 'Cambiar' : 'Subir'}
        </Button>
      )}
    />
  );

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Verificación de Documentos</Text>
        <Text style={styles.subtitle}>
          Sube tus documentos para completar el registro
        </Text>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Documentos Personales</Text>
          
          <DocumentItem
            title="Cédula de Ciudadanía"
            documentKey="cedula"
          />
          <Divider />
          
          <DocumentItem
            title="Recibo de Servicios Públicos"
            documentKey="reciboPago"
          />
          
          {isDriver && (
            <>
              <Divider style={styles.divider} />
              <Text style={styles.sectionTitle}>Documentos del Vehículo</Text>
              
              <DocumentItem
                title="Registro Vehicular"
                documentKey="registroVehicular"
              />
              <Divider />
              
              <DocumentItem
                title="SOAT Vigente"
                documentKey="soat"
              />
              <Divider />
              
              <DocumentItem
                title="Licencia de Conducir"
                documentKey="licenciaConducir"
              />
              <Divider />
              
              <DocumentItem
                title="Foto de la Placa"
                documentKey="placaVehiculo"
              />
            </>
          )}
        </Card.Content>
      </Card>

      <Button
        mode="contained"
        onPress={handleSubmit}
        loading={loading}
        disabled={loading}
        style={styles.submitButton}
      >
        Enviar Documentos
      </Button>

      <Button
        mode="text"
        onPress={() => navigation.goBack()}
        style={styles.backButton}
      >
        Volver
      </Button>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  card: {
    elevation: 4,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    marginTop: 8,
    color: '#333',
  },
  divider: {
    marginVertical: 16,
  },
  submitButton: {
    marginTop: 20,
    paddingVertical: 8,
  },
  backButton: {
    marginTop: 16,
  },
});

export default DocumentUploadScreen;
