import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  Surface,
  Button,
  Card,
  Chip,
  FAB,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import { useUser } from '../context/UserContext';
import { tripService } from '../services/tripService';

const { height } = Dimensions.get('window');

const DriverTripScreen = ({ navigation, route }) => {
  const { currentTrip, startTrip } = useUser();
  const [driverLocation, setDriverLocation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [tripPhase, setTripPhase] = useState('going_to_pickup'); // going_to_pickup, at_pickup, in_progress, completed
  const mapRef = useRef(null);

  useEffect(() => {
    if (currentTrip) {
      // Simular ubicación del conductor (cerca del pasajero)
      const mockDriverLocation = {
        latitude: currentTrip.pickupLocation.latitude + 0.001,
        longitude: currentTrip.pickupLocation.longitude + 0.001,
      };
      setDriverLocation(mockDriverLocation);
      
      // Determinar la fase del viaje
      if (currentTrip.status === 'confirmed') {
        setTripPhase('going_to_pickup');
      } else if (currentTrip.status === 'in_progress') {
        setTripPhase('in_progress');
      }
    }
  }, [currentTrip]);

  const handleNavigateToPickup = async () => {
    try {
      const result = await tripService.openWazeNavigation(
        currentTrip.pickupLocation,
        'Recoger Pasajero'
      );
      
      if (result.success) {
        Alert.alert(
          'Navegación Iniciada 🗺️',
          `Se abrió ${result.app === 'waze' ? 'Waze' : 'Google Maps'} para llevarte al punto de recogida.`,
          [
            {
              text: 'Llegué al punto',
              onPress: () => handleArrivedAtPickup()
            },
            {
              text: 'OK',
              style: 'cancel'
            }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo abrir la navegación');
    }
  };

  const handleArrivedAtPickup = () => {
    setTripPhase('at_pickup');
    Alert.alert(
      'En Punto de Recogida 📍',
      '¿Has llegado al punto de recogida? Contacta al pasajero para coordinar.',
      [
        {
          text: 'Llamar Pasajero',
          onPress: () => {
            // Aquí se podría implementar la llamada
            Alert.alert('Llamando...', `${currentTrip.passenger?.phone || 'Pasajero'}`);
          }
        },
        {
          text: 'Pasajero Abordó',
          onPress: () => handleStartTrip()
        }
      ]
    );
  };

  const handleStartTrip = async () => {
    try {
      setLoading(true);
      const result = await tripService.startTrip(currentTrip._id);
      
      if (result.success) {
        setTripPhase('in_progress');
        startTrip(result.trip);
        
        // Abrir navegación al destino
        const navResult = await tripService.openWazeNavigation(
          currentTrip.destination,
          'Destino del Viaje'
        );
        
        if (navResult.success) {
          Alert.alert(
            'Viaje Iniciado ✅',
            'Navegando al destino. El pasajero pagará al llegar.',
            [
              {
                text: 'Llegamos al destino',
                onPress: () => handleCompleteTrip()
              },
              {
                text: 'OK',
                style: 'cancel'
              }
            ]
          );
        }
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo iniciar el viaje');
    } finally {
      setLoading(false);
    }
  };

  const handleNavigateToDestination = async () => {
    try {
      const result = await tripService.openWazeNavigation(
        currentTrip.destination,
        'Destino del Viaje'
      );
      
      if (result.success) {
        Alert.alert(
          'Navegación al Destino 🎯',
          `Navegando al destino del pasajero.`,
          [
            {
              text: 'Llegamos',
              onPress: () => handleCompleteTrip()
            },
            {
              text: 'OK',
              style: 'cancel'
            }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo abrir la navegación');
    }
  };

  const handleCompleteTrip = () => {
    const earnings = currentTrip.offeredPrice * 0.9;
    const commission = currentTrip.offeredPrice * 0.1;
    
    Alert.alert(
      'Completar Viaje 🎉',
      `¿El pasajero ha pagado $${currentTrip.offeredPrice}?\n\nTus ganancias: $${earnings.toFixed(2)}\nComisión Maclaren: $${commission.toFixed(2)}`,
      [
        { text: 'No, aún no ha pagado', style: 'cancel' },
        {
          text: 'Sí, completar',
          onPress: async () => {
            try {
              setLoading(true);
              const result = await tripService.completeTrip(currentTrip._id);
              
              if (result.success) {
                setTripPhase('completed');
                Alert.alert(
                  'Viaje Completado ✅',
                  `¡Excelente! Has ganado $${earnings.toFixed(2)}.\n\nGracias por usar Maclaren.`,
                  [
                    {
                      text: 'Buscar más viajes',
                      onPress: () => {
                        startTrip(null);
                        navigation.navigate('Map');
                      }
                    }
                  ]
                );
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo completar el viaje');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  if (!currentTrip) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="car-sport" size={64} color="#ccc" />
        <Text style={styles.emptyTitle}>No hay viaje activo</Text>
        <Button
          mode="contained"
          onPress={() => navigation.navigate('Map')}
          style={styles.emptyButton}
          buttonColor="#4CAF50"
        >
          Buscar Viajes
        </Button>
      </View>
    );
  }

  const getPhaseInfo = () => {
    switch (tripPhase) {
      case 'going_to_pickup':
        return {
          title: 'Ir a Recoger 🚗',
          subtitle: 'Dirígete al punto de recogida',
          color: '#2196F3',
          action: handleNavigateToPickup,
          actionText: 'Abrir Navegación'
        };
      case 'at_pickup':
        return {
          title: 'En Punto de Recogida 📍',
          subtitle: 'Esperando al pasajero',
          color: '#FF9800',
          action: handleStartTrip,
          actionText: 'Iniciar Viaje'
        };
      case 'in_progress':
        return {
          title: 'Viaje en Progreso 🎯',
          subtitle: 'Dirigiéndose al destino',
          color: '#4CAF50',
          action: handleNavigateToDestination,
          actionText: 'Navegar al Destino'
        };
      default:
        return {
          title: 'Viaje Activo',
          subtitle: '',
          color: '#4CAF50',
          action: () => {},
          actionText: 'Continuar'
        };
    }
  };

  const phaseInfo = getPhaseInfo();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={phaseInfo.color} />
      
      {/* Header */}
      <Surface style={styles.header} elevation={4}>
        <LinearGradient
          colors={[phaseInfo.color, phaseInfo.color + 'CC']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <Button
              mode="text"
              onPress={() => navigation.goBack()}
              textColor="white"
              icon="arrow-left"
              compact
            >
              Volver
            </Button>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{phaseInfo.title}</Text>
              <Text style={styles.headerSubtitle}>{phaseInfo.subtitle}</Text>
            </View>
            <Chip
              style={styles.priceChip}
              textStyle={styles.priceChipText}
            >
              ${currentTrip.offeredPrice}
            </Chip>
          </View>
        </LinearGradient>
      </Surface>

      {/* Mapa */}
      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={{
          latitude: currentTrip.pickupLocation.latitude,
          longitude: currentTrip.pickupLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        showsUserLocation={true}
        showsTraffic={true}
      >
        {/* Marcador del conductor */}
        {driverLocation && (
          <Marker
            coordinate={driverLocation}
            title="Tu ubicación 🚗"
            pinColor="#4CAF50"
          >
            <View style={styles.driverMarker}>
              <Ionicons name="car-sport" size={24} color="white" />
            </View>
          </Marker>
        )}

        {/* Marcador de recogida */}
        <Marker
          coordinate={currentTrip.pickupLocation}
          title="Punto de Recogida 📍"
          description={`Recoger a ${currentTrip.passenger?.firstName || 'Pasajero'}`}
          pinColor="#ff6b6b"
        >
          <View style={styles.pickupMarker}>
            <Ionicons name="person" size={20} color="white" />
          </View>
        </Marker>

        {/* Marcador de destino */}
        <Marker
          coordinate={currentTrip.destination}
          title="Destino 🎯"
          description="Destino del viaje"
          pinColor="#2196F3"
        >
          <View style={styles.destinationMarker}>
            <Ionicons name="flag" size={20} color="white" />
          </View>
        </Marker>
      </MapView>

      {/* Información del pasajero */}
      <Surface style={styles.passengerCard} elevation={4}>
        <Card.Content style={styles.passengerContent}>
          <View style={styles.passengerHeader}>
            <Ionicons name="person-circle" size={32} color="#4CAF50" />
            <View style={styles.passengerInfo}>
              <Text style={styles.passengerName}>
                {currentTrip.passenger?.firstName || 'Pasajero'} {currentTrip.passenger?.lastName || ''}
              </Text>
              <Text style={styles.passengerPhone}>
                {currentTrip.passenger?.phone || '+507 0000-0000'}
              </Text>
            </View>
            <Button
              mode="outlined"
              onPress={() => Alert.alert('Llamando...', currentTrip.passenger?.phone)}
              icon="phone"
              compact
            >
              Llamar
            </Button>
          </View>
        </Card.Content>
      </Surface>

      {/* Botón de acción principal */}
      <FAB
        style={[styles.actionFab, { backgroundColor: phaseInfo.color }]}
        icon={tripPhase === 'going_to_pickup' ? 'navigation' : 
              tripPhase === 'at_pickup' ? 'play' : 
              tripPhase === 'in_progress' ? 'flag' : 'check'}
        label={phaseInfo.actionText}
        onPress={phaseInfo.action}
        loading={loading}
        disabled={loading}
        color="white"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: StatusBar.currentHeight || 40,
  },
  headerGradient: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    marginLeft: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 2,
  },
  priceChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  priceChipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  map: {
    flex: 1,
  },
  driverMarker: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
    padding: 8,
    borderWidth: 3,
    borderColor: 'white',
  },
  pickupMarker: {
    backgroundColor: '#ff6b6b',
    borderRadius: 15,
    padding: 6,
    borderWidth: 2,
    borderColor: 'white',
  },
  destinationMarker: {
    backgroundColor: '#2196F3',
    borderRadius: 15,
    padding: 6,
    borderWidth: 2,
    borderColor: 'white',
  },
  passengerCard: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    borderRadius: 12,
  },
  passengerContent: {
    padding: 16,
  },
  passengerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passengerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  passengerPhone: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  actionFab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#f5f5f5',
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyButton: {
    marginTop: 24,
    borderRadius: 12,
  },
});

export default DriverTripScreen;
