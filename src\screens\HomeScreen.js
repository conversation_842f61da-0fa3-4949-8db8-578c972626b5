import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, StatusBar, Dimensions } from 'react-native';
import { Card, Text, Button, Switch, FAB, Chip, Avatar, Surface } from 'react-native-paper';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../context/AuthContext';
import { useUser } from '../context/UserContext';
import { paymentService } from '../services/paymentService';
import UserSwitcher from '../components/UserSwitcher';

const HomeScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { userMode, balance, switchMode, updateBalance } = useUser();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadBalance();
  }, []);

  const loadBalance = async () => {
    try {
      const result = await paymentService.getBalance(user.id);
      if (result.success) {
        updateBalance(result.balance);
      }
    } catch (error) {
      console.error('Error loading balance:', error);
    }
  };

  const handleModeSwitch = async () => {
    const newMode = userMode === 'passenger' ? 'driver' : 'passenger';
    
    if (newMode === 'driver' && balance < 10) {
      Alert.alert(
        'Saldo Insuficiente',
        'Necesitas al menos $10 de saldo para recibir viajes como conductor. ¿Deseas recargar ahora?',
        [
          { text: 'Cancelar', style: 'cancel' },
          { text: 'Recargar', onPress: () => navigation.navigate('Payment') },
        ]
      );
      return;
    }

    await switchMode(newMode);
  };

  const handleRecharge = async () => {
    setLoading(true);
    try {
      const result = await paymentService.rechargeBalance(10);
      if (result.success) {
        updateBalance(result.newBalance);
        Alert.alert('Éxito', 'Saldo recargado correctamente');
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo recargar el saldo');
    } finally {
      setLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Buenos días';
    if (hour < 18) return 'Buenas tardes';
    return 'Buenas noches';
  };

  const getModeColor = () => {
    return userMode === 'driver' ? '#4CAF50' : '#2196F3';
  };

  const getModeIcon = () => {
    return userMode === 'driver' ? 'motorcycle' : 'person';
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      <LinearGradient
        colors={['#1a1a2e', '#16213e']}
        style={styles.gradientHeader}
      >
        <View style={styles.header}>
          <View style={styles.userInfo}>
            <Avatar.Text
              size={60}
              label={user?.firstName?.charAt(0) || 'U'}
              style={styles.avatar}
              theme={{ colors: { primary: '#ff6b6b' } }}
            />
            <View style={styles.greetingContainer}>
              <Text style={styles.greeting}>{getGreeting()}, {user?.firstName}! 👋</Text>
              <Text style={styles.appName}>Maclaren</Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Mode Switch Card mejorado */}
        <Surface style={styles.modeCard} elevation={4}>
          <LinearGradient
            colors={userMode === 'driver' ? ['#4CAF50', '#45a049'] : ['#2196F3', '#1976d2']}
            style={styles.modeGradient}
          >
            <View style={styles.modeHeader}>
              <Ionicons
                name={userMode === 'driver' ? 'car-sport' : 'person'}
                size={32}
                color="white"
              />
              <Text style={styles.modeTitle}>
                Modo {userMode === 'driver' ? 'Conductor 🚗' : 'Pasajero 🚶‍♂️'}
              </Text>
            </View>

            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Pasajero</Text>
              <Switch
                value={userMode === 'driver'}
                onValueChange={handleModeSwitch}
                thumbColor="white"
                trackColor={{ false: 'rgba(255,255,255,0.3)', true: 'rgba(255,255,255,0.5)' }}
              />
              <Text style={styles.switchLabel}>Conductor</Text>
            </View>

            {userMode === 'driver' && (
              <View style={styles.balanceContainer}>
                <Ionicons name="wallet" size={20} color="white" />
                <Text style={styles.balanceText}>
                  Saldo: ${(balance || 0).toFixed(2)}
                </Text>
                {(balance || 0) < 10 && (
                  <Ionicons name="warning" size={16} color="#ffeb3b" />
                )}
              </View>
            )}
          </LinearGradient>
        </Surface>

        {/* Balance Card for Drivers */}
        {userMode === 'driver' && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.cardTitle}>Estado del Conductor</Text>

              {(balance || 0) < 10 ? (
                <View style={styles.warningContainer}>
                  <MaterialIcons name="warning" size={24} color="#ff9800" />
                  <Text style={styles.warningText}>
                    Saldo insuficiente para recibir viajes
                  </Text>
                </View>
              ) : (
                <View style={styles.successContainer}>
                  <MaterialIcons name="check-circle" size={24} color="#4CAF50" />
                  <Text style={styles.successText}>
                    Listo para recibir viajes
                  </Text>
                </View>
              )}

              <Button
                mode="contained"
                onPress={handleRecharge}
                loading={loading}
                disabled={loading}
                style={styles.rechargeButton}
                icon="credit-card"
              >
                Recargar $10
              </Button>
            </Card.Content>
          </Card>
        )}

        {/* User Switcher */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>🔄 Cambio Rápido de Usuario</Text>
            <Text style={styles.cardSubtitle}>
              Cambia entre pasajero y conductor sin cerrar la app
            </Text>
            <UserSwitcher style={styles.userSwitcher} />
          </Card.Content>
        </Card>

        {/* Quick Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Acciones Rápidas</Text>

            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Map')}
                style={styles.actionButton}
                icon="map"
              >
                Ver Mapa
              </Button>

              <Button
                mode="outlined"
                onPress={() => navigation.navigate('History')}
                style={styles.actionButton}
                icon="history"
              >
                Historial
              </Button>
            </View>

            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Payment')}
                style={styles.actionButton}
                icon="credit-card"
              >
                Pagos
              </Button>

              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Profile')}
                style={styles.actionButton}
                icon="account"
              >
                Perfil
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Floating Action Button */}
        <FAB
          style={[styles.fab, { backgroundColor: getModeColor() }]}
          icon={userMode === 'driver' ? 'car' : 'plus'}
          onPress={() => navigation.navigate('Map')}
          label={userMode === 'driver' ? 'Buscar Viajes' : 'Solicitar Viaje'}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  gradientHeader: {
    paddingTop: (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: '#ff6b6b',
    marginRight: 15,
  },
  greetingContainer: {
    flex: 1,
  },
  greeting: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
    fontWeight: '300',
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
  },
  modeCard: {
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    marginTop: -10,
  },
  modeGradient: {
    padding: 20,
  },
  modeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  modeTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginLeft: 12,
    color: 'white',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  switchLabel: {
    fontSize: 16,
    marginHorizontal: 12,
    color: 'white',
    fontWeight: '500',
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 12,
    borderRadius: 12,
  },
  balanceText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 8,
    marginRight: 8,
  },
  card: {
    marginBottom: 16,
    elevation: 4,
    borderRadius: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  userSwitcher: {
    marginTop: 8,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#fff3e0',
    borderRadius: 8,
  },
  warningText: {
    marginLeft: 8,
    color: '#f57c00',
    flex: 1,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#e8f5e8',
    borderRadius: 8,
  },
  successText: {
    marginLeft: 8,
    color: '#2e7d32',
    flex: 1,
  },
  rechargeButton: {
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default HomeScreen;
