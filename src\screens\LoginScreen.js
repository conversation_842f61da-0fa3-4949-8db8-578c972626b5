import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, StatusBar, Dimensions } from 'react-native';
import { TextInput, Button, Text, Card, Avatar, Chip } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../context/AuthContext';

const { width, height } = Dimensions.get('window');

const LoginScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }

    setLoading(true);
    const result = await login(email, password);
    setLoading(false);

    if (!result.success) {
      Alert.alert('Error', result.error);
    }
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradientContainer}
      >
        <ScrollView contentContainerStyle={styles.container}>
          {/* Header mejorado */}
          <View style={styles.header}>
            <Avatar.Icon
              size={80}
              icon="car"
              style={styles.avatar}
              theme={{ colors: { primary: '#ff6b6b' } }}
            />
            <Text style={styles.title}>Maclaren</Text>
            <Text style={styles.subtitle}>Tu plataforma de moto-taxi confiable</Text>
          </View>

          {/* Card principal con mejor diseño */}
          <Card style={styles.card} elevation={8}>
            <Card.Content style={styles.cardContent}>
              <Text style={styles.cardTitle}>¡Bienvenido de vuelta! 👋</Text>

              <TextInput
                label="Correo electrónico"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                style={styles.input}
                left={<TextInput.Icon icon="email" />}
                theme={{
                  colors: {
                    primary: '#ff6b6b',
                    outline: '#ddd',
                  }
                }}
              />

              <TextInput
                label="Contraseña"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                secureTextEntry
                style={styles.input}
                left={<TextInput.Icon icon="lock" />}
                theme={{
                  colors: {
                    primary: '#ff6b6b',
                    outline: '#ddd',
                  }
                }}
              />

              <Button
                mode="contained"
                onPress={handleLogin}
                loading={loading}
                disabled={loading}
                style={styles.button}
                buttonColor="#ff6b6b"
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}
              >
                {loading ? 'Iniciando sesión...' : 'Iniciar Sesión 🚀'}
              </Button>

              {/* Credenciales de prueba */}
              <View style={styles.demoContainer}>
                <Text style={styles.demoTitle}>🧪 Credenciales de prueba:</Text>
                <Chip
                  icon="email"
                  style={styles.demoChip}
                  textStyle={styles.demoChipText}
                >
                  <EMAIL>
                </Chip>
                <Chip
                  icon="lock"
                  style={styles.demoChip}
                  textStyle={styles.demoChipText}
                >
                  123456
                </Chip>
              </View>

              <Button
                mode="text"
                onPress={() => navigation.navigate('Register')}
                style={styles.linkButton}
                textColor="#ff6b6b"
              >
                ¿No tienes cuenta? Regístrate aquí ✨
              </Button>
            </Card.Content>
          </Card>
        </ScrollView>
      </LinearGradient>
    </>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
    minHeight: height,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    paddingTop: 20,
  },
  avatar: {
    marginBottom: 20,
    backgroundColor: '#ff6b6b',
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    fontWeight: '300',
  },
  card: {
    marginHorizontal: 10,
    borderRadius: 20,
    backgroundColor: 'white',
    elevation: 8,
  },
  cardContent: {
    padding: 30,
  },
  cardTitle: {
    fontSize: 26,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  input: {
    marginBottom: 20,
    backgroundColor: 'white',
  },
  button: {
    marginTop: 20,
    paddingVertical: 8,
    borderRadius: 12,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  demoContainer: {
    marginTop: 25,
    marginBottom: 15,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    alignItems: 'center',
  },
  demoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 10,
  },
  demoChip: {
    marginVertical: 3,
    backgroundColor: '#e3f2fd',
  },
  demoChipText: {
    fontSize: 12,
    color: '#1976d2',
  },
  linkButton: {
    marginTop: 20,
  },
});

export default LoginScreen;
