import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert, Dimensions, StatusBar, TouchableOpacity } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON> } from 'react-native-maps';
import { FAB, Portal, Modal, Card, Text, TextInput, Button, Surface, Chip, IconButton } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useUser } from '../context/UserContext';
import { tripService } from '../services/tripService';
import { socketService } from '../services/socketService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { height } = Dimensions.get('window');

const MapScreen = ({ navigation }) => {
  const { userMode, currentTrip, updateLocation, startTrip } = useUser();
  const [location, setLocation] = useState(null);
  const [destination, setDestination] = useState(null);
  const [originAddress, setOriginAddress] = useState('');
  const [destinationAddress, setDestinationAddress] = useState('');
  const [availableTrips, setAvailableTrips] = useState([]);
  const [showTripModal, setShowTripModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [searchType, setSearchType] = useState('destination'); // 'origin' or 'destination'
  const [tripPrice, setTripPrice] = useState('');
  const [tripNotes, setTripNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState(null);
  const [showTripDetailModal, setShowTripDetailModal] = useState(false);
  const [acceptedTrip, setAcceptedTrip] = useState(null); // Viaje aceptado por el pasajero
  const [pendingDrivers, setPendingDrivers] = useState([]); // Conductores que han solicitado el viaje
  const [showDriverSelectionModal, setShowDriverSelectionModal] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);
  const mapRef = useRef(null);

  // Inicializar WebSocket y configurar listeners
  const initializeSocket = async () => {
    try {
      console.log('🔌 Iniciando conexión WebSocket...');
      await socketService.connect();

      // Configurar listeners para notificaciones
      socketService.addListener('driver-request-received', handleDriverRequestReceived);
      socketService.addListener('passenger-response', handlePassengerResponse);
      socketService.addListener('trip-status-updated', handleTripStatusUpdated);

      // Si hay un viaje actual, unirse a su sala
      if (currentTrip) {
        console.log('🏠 Uniéndose a sala del viaje existente:', currentTrip._id);
        socketService.joinTripRoom(currentTrip._id);
      }

      console.log('✅ WebSocket inicializado correctamente');

      // Test de conexión
      setTimeout(() => {
        if (socketService.isSocketConnected()) {
          console.log('✅ WebSocket conectado y funcionando');
        } else {
          console.log('❌ WebSocket no está conectado');
        }
      }, 2000);

    } catch (error) {
      console.error('❌ Error inicializando WebSocket:', error);
    }
  };

  // Manejar solicitud de conductor recibida (para pasajeros)
  const handleDriverRequestReceived = (data) => {
    console.log('🚗 Nueva solicitud de conductor recibida:', data);
    console.log('🔍 Estado actual - userMode:', userMode, 'currentTrip:', currentTrip?._id);

    if (userMode === 'passenger' && currentTrip && currentTrip._id === data.tripId) {
      console.log('✅ Procesando solicitud de conductor para pasajero');

      // Agregar el conductor a la lista de pendientes
      setPendingDrivers(prev => {
        const exists = prev.find(d => d.id === data.driverInfo.id);
        if (!exists) {
          const newDrivers = [...prev, data.driverInfo];
          console.log('📋 Conductores actualizados:', newDrivers.length);

          // Mostrar notificación inmediatamente
          setTimeout(() => {
            Alert.alert(
              '🚗 Nuevo Conductor Disponible',
              `${data.driverInfo.firstName} ${data.driverInfo.lastName} quiere aceptar tu viaje.\n\n¿Quieres revisar su solicitud?`,
              [
                { text: 'Después', style: 'cancel' },
                {
                  text: 'Ver Solicitudes',
                  onPress: () => {
                    console.log('👀 Navegando a pantalla de solicitudes');
                    navigation.navigate('Requests');
                  }
                }
              ]
            );
          }, 500);

          return newDrivers;
        }
        return prev;
      });
    } else {
      console.log('❌ No se procesó la solicitud - Condiciones no cumplidas');
    }
  };

  // Manejar respuesta del pasajero (para conductores)
  const handlePassengerResponse = async (data) => {
    console.log('👤 Respuesta del pasajero recibida:', data);

    if (userMode === 'driver') {
      if (data.accepted) {
        // Actualizar el viaje actual
        if (data.trip) {
          startTrip(data.trip);
        }

        // Si se debe abrir navegación automáticamente
        if (data.shouldOpenNavigation && data.pickupLocation) {
          console.log('🗺️ Abriendo navegación automáticamente...');

          try {
            const result = await tripService.openWazeNavigation(
              data.pickupLocation,
              'Recoger Pasajero'
            );

            if (result.success) {
              Alert.alert(
                '✅ ¡Viaje Aceptado!',
                `El pasajero te ha aceptado como conductor.\n\nSe ha abierto ${result.app === 'waze' ? 'Waze' : 'Google Maps'} para llevarte al punto de recogida.`,
                [
                  {
                    text: 'Ver Mapa del Viaje',
                    onPress: () => navigation.navigate('DriverTrip')
                  },
                  {
                    text: 'OK',
                    style: 'cancel'
                  }
                ]
              );
            } else {
              Alert.alert(
                '✅ ¡Viaje Aceptado!',
                'El pasajero te ha aceptado como conductor. Dirígete al punto de recogida.',
                [
                  {
                    text: 'Abrir Navegación',
                    onPress: async () => {
                      await tripService.openWazeNavigation(data.pickupLocation, 'Recoger Pasajero');
                    }
                  },
                  {
                    text: 'Ver Mapa del Viaje',
                    onPress: () => navigation.navigate('DriverTrip')
                  }
                ]
              );
            }
          } catch (error) {
            console.error('❌ Error abriendo navegación:', error);
            Alert.alert(
              '✅ ¡Viaje Aceptado!',
              'El pasajero te ha aceptado como conductor. Dirígete al punto de recogida.',
              [
                {
                  text: 'Ver Mapa del Viaje',
                  onPress: () => navigation.navigate('DriverTrip')
                }
              ]
            );
          }
        } else {
          Alert.alert(
            '✅ ¡Viaje Aceptado!',
            'El pasajero te ha aceptado como conductor. Dirígete al punto de recogida.',
            [
              {
                text: 'Ver Mapa del Viaje',
                onPress: () => navigation.navigate('DriverTrip')
              }
            ]
          );
        }
      } else {
        Alert.alert(
          '❌ Viaje Rechazado',
          'El pasajero ha elegido otro conductor.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  // Manejar actualizaciones de estado del viaje
  const handleTripStatusUpdated = (data) => {
    console.log('📱 Estado del viaje actualizado:', data);

    // Actualizar el estado local según el nuevo estado
    if (currentTrip && currentTrip._id === data.tripId) {
      const updatedTrip = { ...currentTrip, ...data.tripData };
      startTrip(updatedTrip);
    }
  };

  // Función para usar ubicación por defecto
  const useDefaultLocation = () => {
    // Usar ubicación por defecto (Bogotá, Colombia)
    const defaultLocation = {
      latitude: 4.6097,
      longitude: -74.0817,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };

    setLocation(defaultLocation);
    updateLocation(defaultLocation);
    setOriginAddress('Bogotá, Colombia');
    console.log('🏙️ Ubicación por defecto configurada');
  };

  useEffect(() => {
    // Inicializar con ubicación por defecto inmediatamente
    useDefaultLocation();

    // Luego intentar obtener ubicación real
    setTimeout(() => {
      getCurrentLocation();
    }, 1000);

    // Inicializar WebSocket
    initializeSocket();

    // Cargar viajes inmediatamente si es conductor
    if (userMode === 'driver') {
      console.log('🚗 Modo conductor detectado, cargando viajes...');
      loadAvailableTrips();
    }

    const interval = setInterval(() => {
      if (userMode === 'driver') {
        console.log('🔄 Actualizando viajes disponibles...');
        loadAvailableTrips();
      }
    }, 10000); // Actualizar cada 10 segundos

    return () => {
      clearInterval(interval);
      // Limpiar listeners de WebSocket
      socketService.removeListener('driver-request-received', handleDriverRequestReceived);
      socketService.removeListener('passenger-response', handlePassengerResponse);
      socketService.removeListener('trip-status-updated', handleTripStatusUpdated);
    };
  }, [userMode]);

  // useEffect para manejar cambios en el viaje actual
  useEffect(() => {
    if (currentTrip && socketService.isSocketConnected()) {
      console.log('🏠 Uniéndose a la sala del viaje:', currentTrip._id);
      socketService.joinTripRoom(currentTrip._id);
    }
  }, [currentTrip]);

  // useEffect para monitorear estado de WebSocket
  useEffect(() => {
    const checkConnection = () => {
      const isConnected = socketService.isSocketConnected();
      setSocketConnected(isConnected);
      console.log('🔌 Estado WebSocket:', isConnected ? 'Conectado' : 'Desconectado');
    };

    // Verificar cada 3 segundos
    const interval = setInterval(checkConnection, 3000);

    // Verificar inmediatamente
    checkConnection();

    return () => clearInterval(interval);
  }, []);

  const getCurrentLocation = async () => {
    try {
      console.log('🗺️ Solicitando permisos de ubicación...');
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('📍 Estado de permisos:', status);

      if (status !== 'granted') {
        console.log('❌ Permisos de ubicación denegados, usando ubicación por defecto');
        // No mostrar error, simplemente usar ubicación por defecto
        useDefaultLocation();
        return;
      }

      console.log('📡 Obteniendo ubicación actual...');
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeout: 15000, // 15 segundos de timeout
      });

      console.log('✅ Ubicación obtenida:', currentLocation.coords);

      const locationData = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };

      console.log('🗺️ Configurando mapa con ubicación real:', locationData);
      setLocation(locationData);
      updateLocation(locationData);

      // Obtener dirección de la ubicación actual con manejo de errores
      try {
        await reverseGeocode(locationData);
      } catch (geocodeError) {
        console.warn('⚠️ Error en geocodificación, continuando sin dirección:', geocodeError);
        setOriginAddress('Ubicación actual');
      }
    } catch (error) {
      console.error('❌ Error obteniendo ubicación:', error);
      console.log('🏙️ Usando ubicación por defecto (Bogotá)');
      useDefaultLocation();
    }
  };



  // Función para obtener dirección desde coordenadas
  const reverseGeocode = async (coordinates) => {
    try {
      console.log('🔍 Obteniendo dirección para:', coordinates);

      if (!coordinates || !coordinates.latitude || !coordinates.longitude) {
        throw new Error('Coordenadas inválidas');
      }

      const result = await Location.reverseGeocodeAsync({
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      });

      console.log('📍 Resultado de geocodificación:', result);

      if (result && result.length > 0) {
        const address = result[0];
        const addressParts = [];

        if (address.street) addressParts.push(address.street);
        if (address.streetNumber) addressParts.push(address.streetNumber);
        if (address.city) addressParts.push(address.city);
        if (address.region) addressParts.push(address.region);

        const formattedAddress = addressParts.join(', ') || 'Ubicación actual';
        setOriginAddress(formattedAddress);
        console.log('✅ Dirección formateada:', formattedAddress);
      } else {
        setOriginAddress('Ubicación actual');
        console.log('⚠️ No se encontró dirección, usando texto por defecto');
      }
    } catch (error) {
      console.error('❌ Error en geocodificación inversa:', error);
      setOriginAddress('Ubicación actual');
    }
  };



  const loadAvailableTrips = async () => {
    if (!location) {
      console.log('⚠️ No hay ubicación disponible para cargar viajes');
      return;
    }

    try {
      console.log('📍 Cargando viajes disponibles desde:', location);
      const result = await tripService.getAvailableTrips(location);
      console.log('📋 Resultado de viajes:', result);

      if (result.success) {
        console.log(`✅ ${result.trips.length} viajes disponibles encontrados`);
        setAvailableTrips(result.trips);

        // Log de cada viaje para debug
        result.trips.forEach((trip, index) => {
          console.log(`🚗 Viaje ${index + 1}:`, {
            id: trip._id,
            precio: trip.offeredPrice,
            origen: trip.pickupLocation?.address || 'Sin dirección',
            destino: trip.destination?.address || 'Sin dirección'
          });
        });
      } else {
        console.error('❌ Error al cargar viajes:', result.error);
      }
    } catch (error) {
      console.error('❌ Error loading available trips:', error);
    }
  };

  const handleMapPress = (event) => {
    if (userMode === 'passenger' && !currentTrip) {
      const coordinate = event.nativeEvent.coordinate;
      setDestination(coordinate);
      // Obtener dirección del destino seleccionado
      reverseGeocodeDestination(coordinate);
    }
  };

  // Función para obtener dirección del destino
  const reverseGeocodeDestination = async (coordinates) => {
    try {
      console.log('🎯 Obteniendo dirección del destino:', coordinates);

      if (!coordinates || !coordinates.latitude || !coordinates.longitude) {
        throw new Error('Coordenadas del destino inválidas');
      }

      const result = await Location.reverseGeocodeAsync({
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      });

      console.log('📍 Resultado de geocodificación del destino:', result);

      if (result && result.length > 0) {
        const address = result[0];
        const addressParts = [];

        if (address.street) addressParts.push(address.street);
        if (address.streetNumber) addressParts.push(address.streetNumber);
        if (address.city) addressParts.push(address.city);
        if (address.region) addressParts.push(address.region);

        const formattedAddress = addressParts.join(', ') || 'Destino seleccionado';
        setDestinationAddress(formattedAddress);
        console.log('✅ Dirección del destino formateada:', formattedAddress);
      } else {
        setDestinationAddress('Destino seleccionado');
        console.log('⚠️ No se encontró dirección del destino, usando texto por defecto');
      }
    } catch (error) {
      console.error('❌ Error en geocodificación inversa del destino:', error);
      setDestinationAddress('Destino seleccionado');
    }
  };



  // Función para abrir modal de búsqueda
  const openSearchModal = (type) => {
    setSearchType(type);
    setShowSearchModal(true);
  };

  // Función para búsqueda manual
  const handleManualSearch = async (searchText) => {
    if (!searchText || searchText.trim().length < 3) {
      Alert.alert('Error', 'Ingresa al menos 3 caracteres para buscar');
      return;
    }

    try {
      console.log('🔍 Buscando:', searchText);
      const result = await Location.geocodeAsync(searchText + ', Colombia');

      if (result && result.length > 0) {
        const coordinate = {
          latitude: result[0].latitude,
          longitude: result[0].longitude,
        };

        if (searchType === 'origin') {
          setLocation({
            ...coordinate,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
          setOriginAddress(searchText);
          updateLocation(coordinate);
        } else {
          setDestination(coordinate);
          setDestinationAddress(searchText);
        }

        setShowSearchModal(false);

        // Centrar mapa
        if (mapRef.current) {
          mapRef.current.animateToRegion({
            ...coordinate,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }, 1000);
        }
      } else {
        Alert.alert('Error', 'No se encontró la ubicación. Intenta con una dirección más específica.');
      }
    } catch (error) {
      console.error('❌ Error en búsqueda manual:', error);
      Alert.alert('Error', 'Hubo un problema al buscar la ubicación.');
    }
  };

  // Función para ubicaciones rápidas
  const handleQuickLocation = async (type) => {
    try {
      let coordinate;
      let address;

      if (type === 'current') {
        const currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });
        coordinate = {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
        };
        address = 'Mi ubicación actual';
      } else if (type === 'bogota') {
        coordinate = {
          latitude: 4.6097,
          longitude: -74.0817,
        };
        address = 'Centro de Bogotá';
      }

      if (searchType === 'origin') {
        setLocation({
          ...coordinate,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
        setOriginAddress(address);
        updateLocation(coordinate);
      } else {
        setDestination(coordinate);
        setDestinationAddress(address);
      }

      setShowSearchModal(false);

      // Centrar mapa
      if (mapRef.current) {
        mapRef.current.animateToRegion({
          ...coordinate,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error en ubicación rápida:', error);
      Alert.alert('Error', 'No se pudo obtener la ubicación.');
    }
  };

  const handleRequestTrip = async () => {
    if (!location || !destination) {
      Alert.alert('Error', 'Selecciona un origen y destino');
      return;
    }

    if (!tripPrice || parseFloat(tripPrice) <= 0) {
      Alert.alert('Error', 'Ingresa un precio válido');
      return;
    }

    setLoading(true);
    try {
      const tripData = {
        pickupLocation: location,
        destination: destination,
        originAddress: originAddress,
        destinationAddress: destinationAddress,
        offeredPrice: parseFloat(tripPrice),
        notes: tripNotes,
      };

      const result = await tripService.createTripRequest(tripData);

      if (result.success) {
        startTrip(result.trip);
        setShowTripModal(false);
        setTripPrice('');
        setTripNotes('');

        // Unirse a la sala del viaje para recibir notificaciones
        if (socketService.isSocketConnected()) {
          socketService.joinTripRoom(result.trip._id);
        }

        Alert.alert(
          'Viaje Creado ✅',
          'Tu solicitud está activa. Los conductores cercanos podrán enviar propuestas.',
          [
            {
              text: 'Ver Solicitudes',
              onPress: () => navigation.navigate('Requests')
            },
            {
              text: 'Quedarse en Mapa',
              style: 'cancel'
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo crear la solicitud');
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptTrip = async (trip) => {
    Alert.alert(
      'Aceptar Viaje',
      `¿Deseas aceptar este viaje por $${trip.offeredPrice}?\n\nEl pasajero verá tu perfil y decidirá si te acepta como conductor.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Enviar Solicitud',
          onPress: async () => {
            setLoading(true);
            try {
              const result = await tripService.acceptTrip(trip._id);
              if (result.success) {
                setShowTripDetailModal(false);

                // Enviar notificación por WebSocket al pasajero
                const userData = await AsyncStorage.getItem('userData');
                const driverInfo = userData ? JSON.parse(userData) : {
                  id: 'driver-demo',
                  firstName: 'Conductor',
                  lastName: 'Demo',
                  phone: '+507 6000-0000',
                  rating: 4.5,
                  vehicle: {
                    brand: 'Toyota',
                    model: 'Corolla',
                    plate: 'ABC-123'
                  }
                };

                console.log('📤 Enviando solicitud de conductor:', driverInfo);

                // Enviar notificación real por WebSocket
                if (socketService.isSocketConnected()) {
                  socketService.notifyDriverRequest(trip._id, driverInfo);
                  console.log('✅ Solicitud enviada por WebSocket');
                } else {
                  console.log('❌ WebSocket no conectado - No se puede enviar solicitud');
                  Alert.alert(
                    'Error de Conexión',
                    'No se puede enviar la solicitud. Verifica tu conexión a internet.',
                    [{ text: 'OK' }]
                  );
                  return;
                }

                Alert.alert(
                  'Solicitud Enviada ✅',
                  'Tu solicitud ha sido enviada al pasajero. Te notificaremos cuando responda.',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // Actualizar la lista de viajes
                        loadAvailableTrips();
                      }
                    }
                  ]
                );
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo enviar la solicitud');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleNavigateToPassenger = async (trip) => {
    try {
      const result = await tripService.openWazeNavigation(
        trip.pickupLocation,
        'Recoger Pasajero'
      );

      if (result.success) {
        Alert.alert(
          'Navegación Iniciada 🗺️',
          `Se abrió ${result.app === 'waze' ? 'Waze' : 'Google Maps'} para llevarte al punto de recogida.`,
          [
            {
              text: 'Llegué al punto de recogida',
              onPress: () => handleArrivedAtPickup(trip)
            },
            {
              text: 'OK',
              style: 'cancel'
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo iniciar la navegación');
    }
  };

  const handleArrivedAtPickup = (trip) => {
    Alert.alert(
      'Iniciar Viaje 🚗',
      '¿Has recogido al pasajero y estás listo para iniciar el viaje al destino?',
      [
        { text: 'No, aún no', style: 'cancel' },
        {
          text: 'Sí, iniciar viaje',
          onPress: () => handleStartTripToDestination(trip)
        }
      ]
    );
  };

  const handleStartTripToDestination = async (trip) => {
    try {
      // Marcar el viaje como iniciado
      const result = await tripService.startTrip(trip._id);

      if (result.success) {
        // Abrir navegación al destino
        const navResult = await tripService.openWazeNavigation(
          trip.destination,
          'Destino del Viaje'
        );

        if (navResult.success) {
          Alert.alert(
            'Viaje Iniciado ✅',
            `Navegando al destino. El pasajero pagará $${trip.offeredPrice} en efectivo o Yappy al llegar.`,
            [
              {
                text: 'Llegamos al destino',
                onPress: () => handleCompleteTrip(trip)
              },
              {
                text: 'OK',
                style: 'cancel'
              }
            ]
          );

          // Actualizar el estado del viaje actual
          startTrip(result.trip);
        }
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo iniciar el viaje');
    }
  };

  const handleCompleteTrip = (trip) => {
    Alert.alert(
      'Completar Viaje 🎉',
      `¿El pasajero ha pagado $${trip.offeredPrice}?\n\nMaclaren cobrará una comisión del 10% ($${(trip.offeredPrice * 0.1).toFixed(2)}).\nTú recibes: $${(trip.offeredPrice * 0.9).toFixed(2)}`,
      [
        { text: 'No, aún no ha pagado', style: 'cancel' },
        {
          text: 'Sí, completar viaje',
          onPress: async () => {
            try {
              const result = await tripService.completeTrip(trip._id);
              if (result.success) {
                Alert.alert(
                  'Viaje Completado ✅',
                  `¡Excelente! Has ganado $${(trip.offeredPrice * 0.9).toFixed(2)}.\n\nGracias por usar Maclaren.`,
                  [
                    {
                      text: 'Buscar más viajes',
                      onPress: () => {
                        // Limpiar el viaje actual y buscar nuevos viajes
                        startTrip(null);
                        loadAvailableTrips();
                      }
                    }
                  ]
                );
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo completar el viaje');
            }
          }
        }
      ]
    );
  };

  const handleAcceptDriver = async (driver) => {
    try {
      setLoading(true);
      const result = await tripService.passengerAcceptDriver(currentTrip._id, driver.id);

      if (result.success) {
        setShowDriverSelectionModal(false);
        setAcceptedTrip(result.trip);

        // Enviar notificación por WebSocket al conductor
        socketService.notifyPassengerResponse(currentTrip._id, driver.id, true);

        // Limpiar la lista de conductores pendientes
        setPendingDrivers([]);

        Alert.alert(
          'Conductor Aceptado ✅',
          `${driver.firstName} ${driver.lastName} será tu conductor.\n\nTe contactará pronto para coordinar la recogida.`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Actualizar el viaje actual
                startTrip(result.trip);
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo aceptar al conductor');
    } finally {
      setLoading(false);
    }
  };

  const handleRejectDriver = async (driver) => {
    Alert.alert(
      'Rechazar Conductor',
      `¿Estás seguro de que quieres rechazar a ${driver.firstName} ${driver.lastName}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Rechazar',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await tripService.rejectDriver(currentTrip._id, driver.id);

              if (result.success) {
                // Enviar notificación por WebSocket al conductor
                socketService.notifyPassengerResponse(currentTrip._id, driver.id, false);

                // Remover el conductor de la lista
                setPendingDrivers(prev => prev.filter(d => d.id !== driver.id));

                Alert.alert('Conductor Rechazado', 'El conductor ha sido notificado.');
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo rechazar al conductor');
            }
          }
        }
      ]
    );
  };

  // Función para recargar solicitudes pendientes desde el backend
  const loadPendingDriverRequests = async () => {
    if (!currentTrip) return;

    try {
      console.log('🔄 Cargando solicitudes pendientes del backend...');
      // Aquí se implementaría la llamada real al backend
      // const result = await tripService.getPendingDriverRequests(currentTrip._id);

      // Por ahora, mostrar mensaje de que no hay simulaciones
      console.log('ℹ️ Sistema real - Las solicitudes llegarán cuando conductores reales las envíen');

    } catch (error) {
      console.error('❌ Error cargando solicitudes:', error);
    }
  };

  const centerMapOnLocation = () => {
    if (location && mapRef.current) {
      mapRef.current.animateToRegion(location, 1000);
    }
  };

  if (!location) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>🗺️ Cargando Mapa...</Text>
        <Text style={styles.debugText}>
          Configurando mapa de Maclaren para Bogotá
        </Text>
        <Button
          mode="contained"
          onPress={() => {
            useDefaultLocation();
            getCurrentLocation();
          }}
          style={{ marginTop: 20 }}
          buttonColor="#ff6b6b"
        >
          Continuar con Bogotá
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Campos de búsqueda flotantes */}
      <View style={styles.searchContainer}>
        {/* Campo de origen */}
        <TouchableOpacity
          style={styles.searchInput}
          onPress={() => openSearchModal('origin')}
        >
          <Ionicons name="location" size={20} color="#ff6b6b" />
          <Text style={styles.searchText} numberOfLines={1}>
            {originAddress || 'Seleccionar origen'}
          </Text>
          <IconButton icon="magnify" size={20} />
        </TouchableOpacity>

        {/* Campo de destino */}
        <TouchableOpacity
          style={styles.searchInput}
          onPress={() => openSearchModal('destination')}
        >
          <Ionicons name="flag" size={20} color="#4CAF50" />
          <Text style={styles.searchText} numberOfLines={1}>
            {destinationAddress || 'Seleccionar destino'}
          </Text>
          <IconButton icon="magnify" size={20} />
        </TouchableOpacity>
      </View>

      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={location}
        onPress={handleMapPress}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsTraffic={true}
        showsBuildings={true}
        showsCompass={false}
        showsScale={false}
        mapType="standard"
        onMapReady={() => console.log('🗺️ Mapa listo para usar')}
      >
        {/* Marcador de origen */}
        {location && (
          <Marker
            coordinate={location}
            title="Origen 📍"
            description={originAddress}
            pinColor="#ff6b6b"
          >
            <View style={styles.originMarker}>
              <Ionicons name="location" size={24} color="white" />
            </View>
          </Marker>
        )}

        {/* Marcador de destino para pasajeros */}
        {destination && userMode === 'passenger' && (
          <Marker
            coordinate={destination}
            title="Destino 🎯"
            description={destinationAddress}
            pinColor="#4CAF50"
          >
            <View style={styles.destinationMarker}>
              <Ionicons name="flag" size={24} color="white" />
            </View>
          </Marker>
        )}

        {/* Marcadores de viajes disponibles para conductores */}
        {userMode === 'driver' && availableTrips.map((trip) => (
          <Marker
            key={trip._id}
            coordinate={trip.pickupLocation}
            title={`� Maclaren - $${trip.offeredPrice}`}
            description={`${trip.passenger?.firstName || 'Pasajero'} solicita viaje - Toca para ver detalles`}
            pinColor="#4CAF50"
            onPress={() => {
              setSelectedTrip(trip);
              setShowTripDetailModal(true);
            }}
          >
            <View style={styles.tripMarker}>
              <Text style={styles.tripMarkerBrand}>Maclaren</Text>
              <Text style={styles.tripMarkerText}>${trip.offeredPrice}</Text>
              <Ionicons name="car-sport" size={16} color="white" />
            </View>
          </Marker>
        ))}

        {/* Ruta del viaje actual */}
        {currentTrip && currentTrip.route && (
          <Polyline
            coordinates={currentTrip.route}
            strokeColor="#ff6b6b"
            strokeWidth={4}
            strokePattern={[1]}
          />
        )}
      </MapView>

      {/* Header con información del modo */}
      <Surface style={styles.headerContainer} elevation={4}>
        <LinearGradient
          colors={userMode === 'driver' ? ['#4CAF50', '#45a049'] : ['#ff6b6b', '#ee5a24']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <Ionicons
              name={userMode === 'driver' ? 'car-sport' : 'person'}
              size={24}
              color="white"
            />
            <Text style={styles.headerText}>
              {userMode === 'driver' ? 'Modo Conductor 🚗' : 'Modo Pasajero 🚶‍♂️'}
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              {userMode === 'driver' && (
                <Chip
                  style={styles.tripsChip}
                  textStyle={styles.tripsChipText}
                >
                  {availableTrips.length} viajes
                </Chip>
              )}
              <Chip
                style={[styles.connectionChip, { backgroundColor: socketConnected ? '#4CAF50' : '#f44336' }]}
                textStyle={styles.connectionChipText}
                icon={socketConnected ? 'wifi' : 'wifi-off'}
              >
                {socketConnected ? 'Online' : 'Offline'}
              </Chip>
            </View>
          </View>
        </LinearGradient>
      </Surface>

      {/* Instrucciones flotantes */}
      {userMode === 'passenger' && !destination && !currentTrip && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            � Usa los campos de búsqueda para seleccionar origen y destino
          </Text>
        </Surface>
      )}

      {destination && userMode === 'passenger' && !currentTrip && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            ✅ Origen y destino seleccionados. ¡Solicita tu viaje!
          </Text>
        </Surface>
      )}

      {/* Instrucción para pasajero con viaje pendiente */}
      {userMode === 'passenger' && currentTrip && currentTrip.status === 'pending' && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            🔍 Buscando conductores disponibles...
          </Text>
          <Text style={styles.instructionSubtext}>
            Los conductores cercanos verán tu solicitud y podrán enviar propuestas.
          </Text>
          <View style={{ flexDirection: 'row', gap: 8, marginTop: 12 }}>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Requests')}
              style={{ flex: 1, borderRadius: 8 }}
              buttonColor="#ff6b6b"
              icon="account-group"
              compact
            >
              Ver Solicitudes ({pendingDrivers.length})
            </Button>
            <Button
              mode="outlined"
              onPress={() => {
                console.log('🔄 Actualizando solicitudes...');
                loadPendingDriverRequests();
              }}
              style={{ borderRadius: 8 }}
              icon="refresh"
              compact
            >
              Actualizar
            </Button>
          </View>
        </Surface>
      )}

      {/* Instrucción para pasajero con conductor confirmado */}
      {userMode === 'passenger' && currentTrip && currentTrip.status === 'confirmed' && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            🚗 Tu conductor está en camino. Te contactará pronto.
          </Text>
          <Text style={styles.instructionSubtext}>
            Conductor: {currentTrip.driver?.firstName} {currentTrip.driver?.lastName}
          </Text>
        </Surface>
      )}

      {userMode === 'driver' && !currentTrip && availableTrips.length === 0 && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            🔍 Buscando viajes disponibles... Toca "Actualizar Viajes" para refrescar
          </Text>
        </Surface>
      )}

      {userMode === 'driver' && !currentTrip && availableTrips.length > 0 && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            🚗 {availableTrips.length} viaje(s) disponible(s). Toca los marcadores verdes para aceptar
          </Text>
        </Surface>
      )}

      {/* Botón de centrar ubicación mejorado */}
      <FAB
        style={styles.locationFab}
        size="small"
        icon="crosshairs-gps"
        onPress={centerMapOnLocation}
        color="white"
        customSize={48}
      />

      {/* Botón principal según el modo */}
      {userMode === 'passenger' && !currentTrip && (
        <FAB
          style={[styles.mainFab, { backgroundColor: destination ? '#ff6b6b' : '#ccc' }]}
          icon="plus"
          label={destination ? "Solicitar Viaje 🚀" : "Selecciona destino"}
          onPress={() => setShowTripModal(true)}
          disabled={!destination}
          color="white"
        />
      )}

      {userMode === 'driver' && !currentTrip && (
        <View style={styles.driverFabContainer}>
          <FAB
            style={[styles.driverFab, { backgroundColor: '#4CAF50' }]}
            icon="refresh"
            label="Actualizar Viajes 🔄"
            onPress={loadAvailableTrips}
            color="white"
          />
          <FAB
            style={[styles.debugFab, { backgroundColor: '#FF9800' }]}
            icon="bug"
            label="Debug"
            onPress={async () => {
              console.log('🐛 Debug: Limpiando y recargando viajes...');
              setAvailableTrips([]);
              await loadAvailableTrips();
            }}
            color="white"
            size="small"
          />
        </View>
      )}

      {/* Botones para conductor con viaje confirmado */}
      {userMode === 'driver' && currentTrip && currentTrip.status === 'confirmed' && (
        <View style={styles.confirmedTripContainer}>
          <Surface style={styles.tripStatusCard} elevation={4}>
            <Text style={styles.tripStatusTitle}>Viaje Confirmado ✅</Text>
            <Text style={styles.tripStatusText}>
              Pasajero: {currentTrip.passenger?.firstName} {currentTrip.passenger?.lastName}
            </Text>
            <Text style={styles.tripStatusText}>
              Precio: ${currentTrip.offeredPrice}
            </Text>
          </Surface>

          <FAB
            style={[styles.navigationFab, { backgroundColor: '#2196F3' }]}
            icon="map"
            label="Ver Mapa del Viaje 🗺️"
            onPress={() => navigation.navigate('DriverTrip')}
            color="white"
          />
        </View>
      )}

      {/* Botones para viaje en progreso */}
      {userMode === 'driver' && currentTrip && currentTrip.status === 'in_progress' && (
        <View style={styles.confirmedTripContainer}>
          <Surface style={styles.tripStatusCard} elevation={4}>
            <Text style={styles.tripStatusTitle}>Viaje en Progreso 🚗</Text>
            <Text style={styles.tripStatusText}>
              Destino: {currentTrip.destinationAddress}
            </Text>
            <Text style={styles.tripStatusText}>
              Precio: ${currentTrip.offeredPrice}
            </Text>
          </Surface>

          <FAB
            style={[styles.navigationFab, { backgroundColor: '#4CAF50' }]}
            icon="check-circle"
            label="Completar Viaje 🎉"
            onPress={() => handleCompleteTrip(currentTrip)}
            color="white"
          />
        </View>
      )}

      {/* Modal de búsqueda de lugares */}
      <Portal>
        <Modal
          visible={showSearchModal}
          onDismiss={() => setShowSearchModal(false)}
          contentContainerStyle={styles.searchModalContainer}
        >
          <Card style={styles.searchModalCard} elevation={8}>
            <Card.Content style={styles.searchModalContent}>
              <View style={styles.searchModalHeader}>
                <Text style={styles.searchModalTitle}>
                  {searchType === 'origin' ? '📍 Seleccionar Origen' : '🎯 Seleccionar Destino'}
                </Text>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setShowSearchModal(false)}
                />
              </View>

              <View style={styles.searchInputContainer}>
                <TextInput
                  label={searchType === 'origin' ? 'Buscar origen...' : 'Buscar destino...'}
                  mode="outlined"
                  style={styles.searchTextInput}
                  onSubmitEditing={(event) => handleManualSearch(event.nativeEvent.text)}
                  returnKeyType="search"
                  autoFocus={true}
                  left={<TextInput.Icon icon={searchType === 'origin' ? 'map-marker' : 'flag'} />}
                  theme={{ colors: { primary: searchType === 'origin' ? '#ff6b6b' : '#4CAF50' } }}
                />

                <View style={styles.quickOptions}>
                  <Button
                    mode="outlined"
                    onPress={() => handleQuickLocation('current')}
                    style={styles.quickButton}
                    icon="crosshairs-gps"
                    compact
                  >
                    Mi ubicación
                  </Button>

                  <Button
                    mode="outlined"
                    onPress={() => handleQuickLocation('bogota')}
                    style={styles.quickButton}
                    icon="city"
                    compact
                  >
                    Centro Bogotá
                  </Button>
                </View>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* Modal mejorado para solicitar viaje */}
      <Portal>
        <Modal
          visible={showTripModal}
          onDismiss={() => setShowTripModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card style={styles.modalCard} elevation={8}>
            <LinearGradient
              colors={['#ff6b6b', '#ee5a24']}
              style={styles.modalHeader}
            >
              <Ionicons name="car-sport" size={32} color="white" />
              <Text style={styles.modalTitle}>Solicitar Viaje 🚀</Text>
            </LinearGradient>

            <Card.Content style={styles.modalContent}>
              <TextInput
                label="Precio ofrecido ($)"
                value={tripPrice}
                onChangeText={setTripPrice}
                keyboardType="numeric"
                mode="outlined"
                style={styles.input}
                left={<TextInput.Icon icon="currency-usd" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <TextInput
                label="Notas adicionales (opcional)"
                value={tripNotes}
                onChangeText={setTripNotes}
                mode="outlined"
                multiline
                numberOfLines={3}
                style={styles.input}
                left={<TextInput.Icon icon="note-text" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <View style={styles.modalButtons}>
                <Button
                  mode="outlined"
                  onPress={() => setShowTripModal(false)}
                  style={styles.modalButton}
                  textColor="#666"
                >
                  Cancelar
                </Button>

                <Button
                  mode="contained"
                  onPress={handleRequestTrip}
                  loading={loading}
                  disabled={loading}
                  style={styles.modalButton}
                  buttonColor="#ff6b6b"
                >
                  {loading ? 'Solicitando...' : 'Solicitar ✨'}
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* Modal detallado para conductores - Ver detalles del viaje */}
      <Portal>
        <Modal
          visible={showTripDetailModal}
          onDismiss={() => setShowTripDetailModal(false)}
          contentContainerStyle={styles.tripDetailModalContainer}
        >
          <Card style={styles.tripDetailCard} elevation={8}>
            <LinearGradient
              colors={['#4CAF50', '#45a049']}
              style={styles.tripDetailHeader}
            >
              <View style={styles.tripDetailHeaderContent}>
                <Ionicons name="car-sport" size={28} color="white" />
                <Text style={styles.tripDetailTitle}>Detalles del Viaje</Text>
                <Text style={styles.tripDetailPrice}>${selectedTrip?.offeredPrice}</Text>
              </View>
            </LinearGradient>

            {selectedTrip && (
              <Card.Content style={styles.tripDetailContent}>
                {/* Información del pasajero */}
                <View style={styles.passengerInfo}>
                  <View style={styles.passengerHeader}>
                    <Ionicons name="person-circle" size={24} color="#4CAF50" />
                    <Text style={styles.passengerName}>
                      {selectedTrip.passenger?.firstName || 'Pasajero'} {selectedTrip.passenger?.lastName || ''}
                    </Text>
                  </View>
                  {selectedTrip.passenger?.rating && (
                    <View style={styles.ratingContainer}>
                      <Ionicons name="star" size={16} color="#FFD700" />
                      <Text style={styles.ratingText}>{selectedTrip.passenger.rating}</Text>
                    </View>
                  )}
                </View>

                {/* Ubicaciones */}
                <View style={styles.locationSection}>
                  <View style={styles.locationItem}>
                    <Ionicons name="location" size={20} color="#ff6b6b" />
                    <View style={styles.locationText}>
                      <Text style={styles.locationLabel}>Recoger en:</Text>
                      <Text style={styles.locationAddress}>
                        {selectedTrip.pickupLocation?.address || selectedTrip.originAddress || 'Ubicación de recogida'}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.locationDivider} />

                  <View style={styles.locationItem}>
                    <Ionicons name="flag" size={20} color="#4CAF50" />
                    <View style={styles.locationText}>
                      <Text style={styles.locationLabel}>Destino:</Text>
                      <Text style={styles.locationAddress}>
                        {selectedTrip.destination?.address || selectedTrip.destinationAddress || 'Destino del viaje'}
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Información adicional */}
                <View style={styles.tripInfoSection}>
                  <View style={styles.tripInfoItem}>
                    <Ionicons name="time" size={18} color="#666" />
                    <Text style={styles.tripInfoText}>
                      Solicitado: {new Date(selectedTrip.createdAt).toLocaleTimeString('es-ES', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </Text>
                  </View>

                  {selectedTrip.notes && (
                    <View style={styles.tripInfoItem}>
                      <Ionicons name="chatbubble-ellipses" size={18} color="#666" />
                      <Text style={styles.tripInfoText}>Notas: {selectedTrip.notes}</Text>
                    </View>
                  )}

                  <View style={styles.tripInfoItem}>
                    <Ionicons name="cash" size={18} color="#4CAF50" />
                    <Text style={styles.tripInfoText}>
                      Pago: Efectivo o Yappy • Comisión Maclaren: 10%
                    </Text>
                  </View>
                </View>

                {/* Botones de acción */}
                <View style={styles.tripDetailButtons}>
                  <Button
                    mode="outlined"
                    onPress={() => setShowTripDetailModal(false)}
                    style={styles.tripDetailButton}
                    textColor="#666"
                    icon="close"
                  >
                    Cerrar
                  </Button>

                  <Button
                    mode="contained"
                    onPress={() => handleAcceptTrip(selectedTrip)}
                    loading={loading}
                    disabled={loading}
                    style={styles.tripDetailButton}
                    buttonColor="#4CAF50"
                    icon="check-circle"
                  >
                    {loading ? 'Aceptando...' : 'Aceptar Viaje'}
                  </Button>
                </View>
              </Card.Content>
            )}
          </Card>
        </Modal>
      </Portal>

      {/* Modal para selección de conductores (Pasajeros) */}
      <Portal>
        <Modal
          visible={showDriverSelectionModal}
          onDismiss={() => setShowDriverSelectionModal(false)}
          contentContainerStyle={styles.driverSelectionModalContainer}
        >
          <Card style={styles.driverSelectionCard} elevation={8}>
            <LinearGradient
              colors={['#ff6b6b', '#ee5a24']}
              style={styles.driverSelectionHeader}
            >
              <View style={styles.driverSelectionHeaderContent}>
                <Ionicons name="people" size={28} color="white" />
                <Text style={styles.driverSelectionTitle}>Conductores Disponibles</Text>
                <Text style={styles.driverSelectionSubtitle}>
                  {pendingDrivers.length} conductor(es) quieren aceptar tu viaje
                </Text>
              </View>
            </LinearGradient>

            <Card.Content style={styles.driverSelectionContent}>
              {pendingDrivers.map((driver, index) => (
                <View key={driver.id || index} style={styles.driverCard}>
                  <View style={styles.driverInfo}>
                    <View style={styles.driverHeader}>
                      <Ionicons name="person-circle" size={40} color="#4CAF50" />
                      <View style={styles.driverDetails}>
                        <Text style={styles.driverName}>
                          {driver.firstName} {driver.lastName}
                        </Text>
                        <Text style={styles.driverPhone}>{driver.phone}</Text>
                        {driver.vehicle && (
                          <Text style={styles.driverVehicle}>
                            🚗 {driver.vehicle.brand} {driver.vehicle.model} - {driver.vehicle.plate}
                          </Text>
                        )}
                      </View>
                      <View style={styles.driverRating}>
                        <Ionicons name="star" size={16} color="#FFD700" />
                        <Text style={styles.driverRatingText}>{driver.rating || '4.5'}</Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.driverActions}>
                    <Button
                      mode="outlined"
                      onPress={() => handleRejectDriver(driver)}
                      style={styles.driverActionButton}
                      textColor="#ff6b6b"
                      icon="close"
                      compact
                    >
                      Rechazar
                    </Button>

                    <Button
                      mode="contained"
                      onPress={() => handleAcceptDriver(driver)}
                      style={styles.driverActionButton}
                      buttonColor="#4CAF50"
                      icon="check"
                      compact
                    >
                      Aceptar
                    </Button>
                  </View>
                </View>
              ))}

              {pendingDrivers.length === 0 && (
                <View style={styles.noDriversContainer}>
                  <Ionicons name="car-sport" size={48} color="#ccc" />
                  <Text style={styles.noDriversText}>
                    Esperando conductores...
                  </Text>
                  <Text style={styles.noDriversSubtext}>
                    Los conductores cercanos verán tu solicitud y podrán aceptarla.
                  </Text>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      console.log('🔄 Actualizando solicitudes desde modal...');
                      loadPendingDriverRequests();
                    }}
                    style={{ marginTop: 16, borderRadius: 8 }}
                    icon="refresh"
                    compact
                  >
                    Actualizar Solicitudes
                  </Button>
                </View>
              )}

              <View style={styles.driverSelectionButtons}>
                <Button
                  mode="outlined"
                  onPress={() => setShowDriverSelectionModal(false)}
                  style={styles.driverSelectionButton}
                  textColor="#666"
                  icon="close"
                >
                  Cerrar
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  searchContainer: {
    position: 'absolute',
    top: StatusBar.currentHeight + 10,
    left: 16,
    right: 16,
    zIndex: 1000,
  },
  searchInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  originMarker: {
    backgroundColor: '#ff6b6b',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  destinationMarker: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  tripMarker: {
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  tripMarkerText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  debugText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  headerContainer: {
    position: 'absolute',
    top: StatusBar.currentHeight + 10,
    left: 16,
    right: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  tripsChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tripsChipText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  connectionChip: {
    height: 28,
  },
  connectionChipText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  instructionCard: {
    position: 'absolute',
    top: StatusBar.currentHeight + 80,
    left: 16,
    right: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontWeight: '500',
  },
  instructionSubtext: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginTop: 4,
  },
  locationFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    top: StatusBar.currentHeight + 120,
    backgroundColor: '#ff6b6b',
  },
  mainFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 100,
  },
  driverFabContainer: {
    position: 'absolute',
    right: 16,
    bottom: 100,
    alignItems: 'flex-end',
  },
  driverFab: {
    marginBottom: 8,
  },
  debugFab: {
    marginBottom: 8,
  },
  modalContainer: {
    margin: 20,
  },
  modalCard: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 12,
  },
  modalContent: {
    padding: 20,
  },
  input: {
    marginBottom: 16,
    backgroundColor: 'white',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 8,
    borderRadius: 12,
  },
  // Estilos para modal de búsqueda
  searchModalContainer: {
    margin: 20,
    marginTop: StatusBar.currentHeight + 60,
  },
  searchModalCard: {
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: height * 0.8,
  },
  searchModalContent: {
    padding: 0,
  },
  searchModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  searchModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  // Estilos para Google Places Autocomplete
  autocompleteContainer: {
    flex: 0,
    padding: 16,
  },
  autocompleteInputContainer: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
  },
  autocompleteInput: {
    fontSize: 16,
    color: '#333',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  autocompleteList: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginTop: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  autocompleteRow: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  autocompleteDescription: {
    fontSize: 14,
    color: '#333',
  },
  // Estilos para el nuevo sistema de búsqueda
  searchInputContainer: {
    padding: 16,
  },
  searchTextInput: {
    marginBottom: 16,
    backgroundColor: 'white',
  },
  quickOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
  },
  quickButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  // Estilos para modal de detalles del viaje
  tripDetailModalContainer: {
    margin: 16,
    marginTop: StatusBar.currentHeight + 40,
  },
  tripDetailCard: {
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: height * 0.85,
  },
  tripDetailHeader: {
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  tripDetailHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tripDetailTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    marginLeft: 12,
  },
  tripDetailPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  tripDetailContent: {
    padding: 20,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
  },
  passengerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  passengerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 4,
  },
  locationSection: {
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 8,
  },
  locationText: {
    flex: 1,
    marginLeft: 12,
  },
  locationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  locationDivider: {
    height: 1,
    backgroundColor: '#e9ecef',
    marginVertical: 12,
    marginLeft: 32,
  },
  tripInfoSection: {
    marginBottom: 20,
  },
  tripInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  tripInfoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
  tripDetailButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  tripDetailButton: {
    flex: 1,
    borderRadius: 12,
  },
  tripMarkerBrand: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
    marginBottom: 1,
  },
  // Estilos para viaje confirmado
  confirmedTripContainer: {
    position: 'absolute',
    right: 16,
    bottom: 100,
    alignItems: 'flex-end',
  },
  tripStatusCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    minWidth: 200,
  },
  tripStatusTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 8,
  },
  tripStatusText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  navigationFab: {
    marginBottom: 8,
  },
  // Estilos para modal de selección de conductores
  driverSelectionModalContainer: {
    margin: 16,
    marginTop: StatusBar.currentHeight + 40,
  },
  driverSelectionCard: {
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: height * 0.85,
  },
  driverSelectionHeader: {
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  driverSelectionHeaderContent: {
    alignItems: 'center',
  },
  driverSelectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 8,
    textAlign: 'center',
  },
  driverSelectionSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
    textAlign: 'center',
  },
  driverSelectionContent: {
    padding: 20,
  },
  driverCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  driverInfo: {
    marginBottom: 12,
  },
  driverHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverDetails: {
    flex: 1,
    marginLeft: 12,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  driverPhone: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  driverVehicle: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '500',
  },
  driverRating: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  driverRatingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 4,
  },
  driverActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  driverActionButton: {
    flex: 1,
    borderRadius: 8,
  },
  noDriversContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  noDriversText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  noDriversSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  driverSelectionButtons: {
    marginTop: 20,
  },
  driverSelectionButton: {
    borderRadius: 12,
  },
});

export default MapScreen;
