import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert, Dimensions, StatusBar, TouchableOpacity } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON> } from 'react-native-maps';
import { FAB, Portal, Modal, Card, Text, TextInput, Button, Surface, Chip, IconButton } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useUser } from '../context/UserContext';
import { tripService } from '../services/tripService';

const { height } = Dimensions.get('window');

const MapScreen = () => {
  const { userMode, currentTrip, updateLocation, startTrip } = useUser();
  const [location, setLocation] = useState(null);
  const [destination, setDestination] = useState(null);
  const [originAddress, setOriginAddress] = useState('');
  const [destinationAddress, setDestinationAddress] = useState('');
  const [availableTrips, setAvailableTrips] = useState([]);
  const [showTripModal, setShowTripModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [searchType, setSearchType] = useState('destination'); // 'origin' or 'destination'
  const [tripPrice, setTripPrice] = useState('');
  const [tripNotes, setTripNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState(null);
  const [showTripDetailModal, setShowTripDetailModal] = useState(false);
  const mapRef = useRef(null);

  // Función para usar ubicación por defecto
  const useDefaultLocation = () => {
    // Usar ubicación por defecto (Bogotá, Colombia)
    const defaultLocation = {
      latitude: 4.6097,
      longitude: -74.0817,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };

    setLocation(defaultLocation);
    updateLocation(defaultLocation);
    setOriginAddress('Bogotá, Colombia');
    console.log('🏙️ Ubicación por defecto configurada');
  };

  useEffect(() => {
    // Inicializar con ubicación por defecto inmediatamente
    useDefaultLocation();

    // Luego intentar obtener ubicación real
    setTimeout(() => {
      getCurrentLocation();
    }, 1000);

    // Cargar viajes inmediatamente si es conductor
    if (userMode === 'driver') {
      console.log('🚗 Modo conductor detectado, cargando viajes...');
      loadAvailableTrips();
    }

    const interval = setInterval(() => {
      if (userMode === 'driver') {
        console.log('🔄 Actualizando viajes disponibles...');
        loadAvailableTrips();
      }
    }, 10000); // Actualizar cada 10 segundos

    return () => clearInterval(interval);
  }, [userMode]);

  const getCurrentLocation = async () => {
    try {
      console.log('🗺️ Solicitando permisos de ubicación...');
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('📍 Estado de permisos:', status);

      if (status !== 'granted') {
        console.log('❌ Permisos de ubicación denegados, usando ubicación por defecto');
        // No mostrar error, simplemente usar ubicación por defecto
        useDefaultLocation();
        return;
      }

      console.log('📡 Obteniendo ubicación actual...');
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeout: 15000, // 15 segundos de timeout
      });

      console.log('✅ Ubicación obtenida:', currentLocation.coords);

      const locationData = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };

      console.log('🗺️ Configurando mapa con ubicación real:', locationData);
      setLocation(locationData);
      updateLocation(locationData);

      // Obtener dirección de la ubicación actual con manejo de errores
      try {
        await reverseGeocode(locationData);
      } catch (geocodeError) {
        console.warn('⚠️ Error en geocodificación, continuando sin dirección:', geocodeError);
        setOriginAddress('Ubicación actual');
      }
    } catch (error) {
      console.error('❌ Error obteniendo ubicación:', error);
      console.log('🏙️ Usando ubicación por defecto (Bogotá)');
      useDefaultLocation();
    }
  };



  // Función para obtener dirección desde coordenadas
  const reverseGeocode = async (coordinates) => {
    try {
      console.log('🔍 Obteniendo dirección para:', coordinates);

      if (!coordinates || !coordinates.latitude || !coordinates.longitude) {
        throw new Error('Coordenadas inválidas');
      }

      const result = await Location.reverseGeocodeAsync({
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      });

      console.log('📍 Resultado de geocodificación:', result);

      if (result && result.length > 0) {
        const address = result[0];
        const addressParts = [];

        if (address.street) addressParts.push(address.street);
        if (address.streetNumber) addressParts.push(address.streetNumber);
        if (address.city) addressParts.push(address.city);
        if (address.region) addressParts.push(address.region);

        const formattedAddress = addressParts.join(', ') || 'Ubicación actual';
        setOriginAddress(formattedAddress);
        console.log('✅ Dirección formateada:', formattedAddress);
      } else {
        setOriginAddress('Ubicación actual');
        console.log('⚠️ No se encontró dirección, usando texto por defecto');
      }
    } catch (error) {
      console.error('❌ Error en geocodificación inversa:', error);
      setOriginAddress('Ubicación actual');
    }
  };



  const loadAvailableTrips = async () => {
    if (!location) {
      console.log('⚠️ No hay ubicación disponible para cargar viajes');
      return;
    }

    try {
      console.log('📍 Cargando viajes disponibles desde:', location);
      const result = await tripService.getAvailableTrips(location);
      console.log('📋 Resultado de viajes:', result);

      if (result.success) {
        console.log(`✅ ${result.trips.length} viajes disponibles encontrados`);
        setAvailableTrips(result.trips);

        // Log de cada viaje para debug
        result.trips.forEach((trip, index) => {
          console.log(`🚗 Viaje ${index + 1}:`, {
            id: trip._id,
            precio: trip.offeredPrice,
            origen: trip.pickupLocation?.address || 'Sin dirección',
            destino: trip.destination?.address || 'Sin dirección'
          });
        });
      } else {
        console.error('❌ Error al cargar viajes:', result.error);
      }
    } catch (error) {
      console.error('❌ Error loading available trips:', error);
    }
  };

  const handleMapPress = (event) => {
    if (userMode === 'passenger' && !currentTrip) {
      const coordinate = event.nativeEvent.coordinate;
      setDestination(coordinate);
      // Obtener dirección del destino seleccionado
      reverseGeocodeDestination(coordinate);
    }
  };

  // Función para obtener dirección del destino
  const reverseGeocodeDestination = async (coordinates) => {
    try {
      console.log('🎯 Obteniendo dirección del destino:', coordinates);

      if (!coordinates || !coordinates.latitude || !coordinates.longitude) {
        throw new Error('Coordenadas del destino inválidas');
      }

      const result = await Location.reverseGeocodeAsync({
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      });

      console.log('📍 Resultado de geocodificación del destino:', result);

      if (result && result.length > 0) {
        const address = result[0];
        const addressParts = [];

        if (address.street) addressParts.push(address.street);
        if (address.streetNumber) addressParts.push(address.streetNumber);
        if (address.city) addressParts.push(address.city);
        if (address.region) addressParts.push(address.region);

        const formattedAddress = addressParts.join(', ') || 'Destino seleccionado';
        setDestinationAddress(formattedAddress);
        console.log('✅ Dirección del destino formateada:', formattedAddress);
      } else {
        setDestinationAddress('Destino seleccionado');
        console.log('⚠️ No se encontró dirección del destino, usando texto por defecto');
      }
    } catch (error) {
      console.error('❌ Error en geocodificación inversa del destino:', error);
      setDestinationAddress('Destino seleccionado');
    }
  };



  // Función para abrir modal de búsqueda
  const openSearchModal = (type) => {
    setSearchType(type);
    setShowSearchModal(true);
  };

  // Función para búsqueda manual
  const handleManualSearch = async (searchText) => {
    if (!searchText || searchText.trim().length < 3) {
      Alert.alert('Error', 'Ingresa al menos 3 caracteres para buscar');
      return;
    }

    try {
      console.log('🔍 Buscando:', searchText);
      const result = await Location.geocodeAsync(searchText + ', Colombia');

      if (result && result.length > 0) {
        const coordinate = {
          latitude: result[0].latitude,
          longitude: result[0].longitude,
        };

        if (searchType === 'origin') {
          setLocation({
            ...coordinate,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
          setOriginAddress(searchText);
          updateLocation(coordinate);
        } else {
          setDestination(coordinate);
          setDestinationAddress(searchText);
        }

        setShowSearchModal(false);

        // Centrar mapa
        if (mapRef.current) {
          mapRef.current.animateToRegion({
            ...coordinate,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }, 1000);
        }
      } else {
        Alert.alert('Error', 'No se encontró la ubicación. Intenta con una dirección más específica.');
      }
    } catch (error) {
      console.error('❌ Error en búsqueda manual:', error);
      Alert.alert('Error', 'Hubo un problema al buscar la ubicación.');
    }
  };

  // Función para ubicaciones rápidas
  const handleQuickLocation = async (type) => {
    try {
      let coordinate;
      let address;

      if (type === 'current') {
        const currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });
        coordinate = {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
        };
        address = 'Mi ubicación actual';
      } else if (type === 'bogota') {
        coordinate = {
          latitude: 4.6097,
          longitude: -74.0817,
        };
        address = 'Centro de Bogotá';
      }

      if (searchType === 'origin') {
        setLocation({
          ...coordinate,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
        setOriginAddress(address);
        updateLocation(coordinate);
      } else {
        setDestination(coordinate);
        setDestinationAddress(address);
      }

      setShowSearchModal(false);

      // Centrar mapa
      if (mapRef.current) {
        mapRef.current.animateToRegion({
          ...coordinate,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error en ubicación rápida:', error);
      Alert.alert('Error', 'No se pudo obtener la ubicación.');
    }
  };

  const handleRequestTrip = async () => {
    if (!location || !destination) {
      Alert.alert('Error', 'Selecciona un origen y destino');
      return;
    }

    if (!tripPrice || parseFloat(tripPrice) <= 0) {
      Alert.alert('Error', 'Ingresa un precio válido');
      return;
    }

    setLoading(true);
    try {
      const tripData = {
        pickupLocation: location,
        destination: destination,
        originAddress: originAddress,
        destinationAddress: destinationAddress,
        offeredPrice: parseFloat(tripPrice),
        notes: tripNotes,
      };

      const result = await tripService.createTripRequest(tripData);

      if (result.success) {
        startTrip(result.trip);
        setShowTripModal(false);
        setTripPrice('');
        setTripNotes('');
        Alert.alert('Éxito', 'Solicitud de viaje creada. Esperando conductor...');
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo crear la solicitud');
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptTrip = async (trip) => {
    Alert.alert(
      'Aceptar Viaje',
      `¿Deseas aceptar este viaje por $${trip.offeredPrice}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Aceptar',
          onPress: async () => {
            setLoading(true);
            try {
              const result = await tripService.acceptTrip(trip._id);
              if (result.success) {
                startTrip(result.trip);
                Alert.alert('Éxito', 'Viaje aceptado. Dirígete al punto de recogida.');
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo aceptar el viaje');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const centerMapOnLocation = () => {
    if (location && mapRef.current) {
      mapRef.current.animateToRegion(location, 1000);
    }
  };

  if (!location) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>🗺️ Cargando Mapa...</Text>
        <Text style={styles.debugText}>
          Configurando mapa de Maclaren para Bogotá
        </Text>
        <Button
          mode="contained"
          onPress={() => {
            useDefaultLocation();
            getCurrentLocation();
          }}
          style={{ marginTop: 20 }}
          buttonColor="#ff6b6b"
        >
          Continuar con Bogotá
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Campos de búsqueda flotantes */}
      <View style={styles.searchContainer}>
        {/* Campo de origen */}
        <TouchableOpacity
          style={styles.searchInput}
          onPress={() => openSearchModal('origin')}
        >
          <Ionicons name="location" size={20} color="#ff6b6b" />
          <Text style={styles.searchText} numberOfLines={1}>
            {originAddress || 'Seleccionar origen'}
          </Text>
          <IconButton icon="magnify" size={20} />
        </TouchableOpacity>

        {/* Campo de destino */}
        <TouchableOpacity
          style={styles.searchInput}
          onPress={() => openSearchModal('destination')}
        >
          <Ionicons name="flag" size={20} color="#4CAF50" />
          <Text style={styles.searchText} numberOfLines={1}>
            {destinationAddress || 'Seleccionar destino'}
          </Text>
          <IconButton icon="magnify" size={20} />
        </TouchableOpacity>
      </View>

      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={location}
        onPress={handleMapPress}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsTraffic={true}
        showsBuildings={true}
        showsCompass={false}
        showsScale={false}
        mapType="standard"
        onMapReady={() => console.log('🗺️ Mapa listo para usar')}
      >
        {/* Marcador de origen */}
        {location && (
          <Marker
            coordinate={location}
            title="Origen 📍"
            description={originAddress}
            pinColor="#ff6b6b"
          >
            <View style={styles.originMarker}>
              <Ionicons name="location" size={24} color="white" />
            </View>
          </Marker>
        )}

        {/* Marcador de destino para pasajeros */}
        {destination && userMode === 'passenger' && (
          <Marker
            coordinate={destination}
            title="Destino 🎯"
            description={destinationAddress}
            pinColor="#4CAF50"
          >
            <View style={styles.destinationMarker}>
              <Ionicons name="flag" size={24} color="white" />
            </View>
          </Marker>
        )}

        {/* Marcadores de viajes disponibles para conductores */}
        {userMode === 'driver' && availableTrips.map((trip) => (
          <Marker
            key={trip._id}
            coordinate={trip.pickupLocation}
            title={`� Maclaren - $${trip.offeredPrice}`}
            description={`${trip.passenger?.firstName || 'Pasajero'} solicita viaje - Toca para ver detalles`}
            pinColor="#4CAF50"
            onPress={() => {
              setSelectedTrip(trip);
              setShowTripDetailModal(true);
            }}
          >
            <View style={styles.tripMarker}>
              <Text style={styles.tripMarkerBrand}>Maclaren</Text>
              <Text style={styles.tripMarkerText}>${trip.offeredPrice}</Text>
              <Ionicons name="car-sport" size={16} color="white" />
            </View>
          </Marker>
        ))}

        {/* Ruta del viaje actual */}
        {currentTrip && currentTrip.route && (
          <Polyline
            coordinates={currentTrip.route}
            strokeColor="#ff6b6b"
            strokeWidth={4}
            strokePattern={[1]}
          />
        )}
      </MapView>

      {/* Header con información del modo */}
      <Surface style={styles.headerContainer} elevation={4}>
        <LinearGradient
          colors={userMode === 'driver' ? ['#4CAF50', '#45a049'] : ['#ff6b6b', '#ee5a24']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <Ionicons
              name={userMode === 'driver' ? 'car-sport' : 'person'}
              size={24}
              color="white"
            />
            <Text style={styles.headerText}>
              {userMode === 'driver' ? 'Modo Conductor 🚗' : 'Modo Pasajero 🚶‍♂️'}
            </Text>
            {userMode === 'driver' && (
              <Chip
                style={styles.tripsChip}
                textStyle={styles.tripsChipText}
              >
                {availableTrips.length} viajes
              </Chip>
            )}
          </View>
        </LinearGradient>
      </Surface>

      {/* Instrucciones flotantes */}
      {userMode === 'passenger' && !destination && !currentTrip && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            � Usa los campos de búsqueda para seleccionar origen y destino
          </Text>
        </Surface>
      )}

      {destination && userMode === 'passenger' && !currentTrip && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            ✅ Origen y destino seleccionados. ¡Solicita tu viaje!
          </Text>
        </Surface>
      )}

      {userMode === 'driver' && !currentTrip && availableTrips.length === 0 && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            🔍 Buscando viajes disponibles... Toca "Actualizar Viajes" para refrescar
          </Text>
        </Surface>
      )}

      {userMode === 'driver' && !currentTrip && availableTrips.length > 0 && (
        <Surface style={styles.instructionCard} elevation={2}>
          <Text style={styles.instructionText}>
            🚗 {availableTrips.length} viaje(s) disponible(s). Toca los marcadores verdes para aceptar
          </Text>
        </Surface>
      )}

      {/* Botón de centrar ubicación mejorado */}
      <FAB
        style={styles.locationFab}
        size="small"
        icon="crosshairs-gps"
        onPress={centerMapOnLocation}
        color="white"
        customSize={48}
      />

      {/* Botón principal según el modo */}
      {userMode === 'passenger' && !currentTrip && (
        <FAB
          style={[styles.mainFab, { backgroundColor: destination ? '#ff6b6b' : '#ccc' }]}
          icon="plus"
          label={destination ? "Solicitar Viaje 🚀" : "Selecciona destino"}
          onPress={() => setShowTripModal(true)}
          disabled={!destination}
          color="white"
        />
      )}

      {userMode === 'driver' && !currentTrip && (
        <View style={styles.driverFabContainer}>
          <FAB
            style={[styles.driverFab, { backgroundColor: '#4CAF50' }]}
            icon="refresh"
            label="Actualizar Viajes 🔄"
            onPress={loadAvailableTrips}
            color="white"
          />
          <FAB
            style={[styles.debugFab, { backgroundColor: '#FF9800' }]}
            icon="bug"
            label="Debug"
            onPress={async () => {
              console.log('🐛 Debug: Limpiando y recargando viajes...');
              setAvailableTrips([]);
              await loadAvailableTrips();
            }}
            color="white"
            size="small"
          />
        </View>
      )}

      {/* Modal de búsqueda de lugares */}
      <Portal>
        <Modal
          visible={showSearchModal}
          onDismiss={() => setShowSearchModal(false)}
          contentContainerStyle={styles.searchModalContainer}
        >
          <Card style={styles.searchModalCard} elevation={8}>
            <Card.Content style={styles.searchModalContent}>
              <View style={styles.searchModalHeader}>
                <Text style={styles.searchModalTitle}>
                  {searchType === 'origin' ? '📍 Seleccionar Origen' : '🎯 Seleccionar Destino'}
                </Text>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setShowSearchModal(false)}
                />
              </View>

              <View style={styles.searchInputContainer}>
                <TextInput
                  label={searchType === 'origin' ? 'Buscar origen...' : 'Buscar destino...'}
                  mode="outlined"
                  style={styles.searchTextInput}
                  onSubmitEditing={(event) => handleManualSearch(event.nativeEvent.text)}
                  returnKeyType="search"
                  autoFocus={true}
                  left={<TextInput.Icon icon={searchType === 'origin' ? 'map-marker' : 'flag'} />}
                  theme={{ colors: { primary: searchType === 'origin' ? '#ff6b6b' : '#4CAF50' } }}
                />

                <View style={styles.quickOptions}>
                  <Button
                    mode="outlined"
                    onPress={() => handleQuickLocation('current')}
                    style={styles.quickButton}
                    icon="crosshairs-gps"
                    compact
                  >
                    Mi ubicación
                  </Button>

                  <Button
                    mode="outlined"
                    onPress={() => handleQuickLocation('bogota')}
                    style={styles.quickButton}
                    icon="city"
                    compact
                  >
                    Centro Bogotá
                  </Button>
                </View>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* Modal mejorado para solicitar viaje */}
      <Portal>
        <Modal
          visible={showTripModal}
          onDismiss={() => setShowTripModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card style={styles.modalCard} elevation={8}>
            <LinearGradient
              colors={['#ff6b6b', '#ee5a24']}
              style={styles.modalHeader}
            >
              <Ionicons name="car-sport" size={32} color="white" />
              <Text style={styles.modalTitle}>Solicitar Viaje 🚀</Text>
            </LinearGradient>

            <Card.Content style={styles.modalContent}>
              <TextInput
                label="Precio ofrecido ($)"
                value={tripPrice}
                onChangeText={setTripPrice}
                keyboardType="numeric"
                mode="outlined"
                style={styles.input}
                left={<TextInput.Icon icon="currency-usd" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <TextInput
                label="Notas adicionales (opcional)"
                value={tripNotes}
                onChangeText={setTripNotes}
                mode="outlined"
                multiline
                numberOfLines={3}
                style={styles.input}
                left={<TextInput.Icon icon="note-text" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <View style={styles.modalButtons}>
                <Button
                  mode="outlined"
                  onPress={() => setShowTripModal(false)}
                  style={styles.modalButton}
                  textColor="#666"
                >
                  Cancelar
                </Button>

                <Button
                  mode="contained"
                  onPress={handleRequestTrip}
                  loading={loading}
                  disabled={loading}
                  style={styles.modalButton}
                  buttonColor="#ff6b6b"
                >
                  {loading ? 'Solicitando...' : 'Solicitar ✨'}
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  searchContainer: {
    position: 'absolute',
    top: StatusBar.currentHeight + 10,
    left: 16,
    right: 16,
    zIndex: 1000,
  },
  searchInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  originMarker: {
    backgroundColor: '#ff6b6b',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  destinationMarker: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  tripMarker: {
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  tripMarkerText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  debugText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  headerContainer: {
    position: 'absolute',
    top: StatusBar.currentHeight + 10,
    left: 16,
    right: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  tripsChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tripsChipText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  instructionCard: {
    position: 'absolute',
    top: StatusBar.currentHeight + 80,
    left: 16,
    right: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontWeight: '500',
  },
  locationFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    top: StatusBar.currentHeight + 120,
    backgroundColor: '#ff6b6b',
  },
  mainFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 100,
  },
  driverFabContainer: {
    position: 'absolute',
    right: 16,
    bottom: 100,
    alignItems: 'flex-end',
  },
  driverFab: {
    marginBottom: 8,
  },
  debugFab: {
    marginBottom: 8,
  },
  modalContainer: {
    margin: 20,
  },
  modalCard: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 12,
  },
  modalContent: {
    padding: 20,
  },
  input: {
    marginBottom: 16,
    backgroundColor: 'white',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 8,
    borderRadius: 12,
  },
  // Estilos para modal de búsqueda
  searchModalContainer: {
    margin: 20,
    marginTop: StatusBar.currentHeight + 60,
  },
  searchModalCard: {
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: height * 0.8,
  },
  searchModalContent: {
    padding: 0,
  },
  searchModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  searchModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  // Estilos para Google Places Autocomplete
  autocompleteContainer: {
    flex: 0,
    padding: 16,
  },
  autocompleteInputContainer: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
  },
  autocompleteInput: {
    fontSize: 16,
    color: '#333',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  autocompleteList: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginTop: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  autocompleteRow: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  autocompleteDescription: {
    fontSize: 14,
    color: '#333',
  },
  // Estilos para el nuevo sistema de búsqueda
  searchInputContainer: {
    padding: 16,
  },
  searchTextInput: {
    marginBottom: 16,
    backgroundColor: 'white',
  },
  quickOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
  },
  quickButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default MapScreen;
