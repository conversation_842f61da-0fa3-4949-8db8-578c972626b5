import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, RefreshControl } from 'react-native';
import { Card, Text, Button, List, Divider, ActivityIndicator, Chip } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useUser } from '../context/UserContext';
import { paymentService } from '../services/paymentService';

const PaymentScreen = () => {
  const { user } = useAuth();
  const { userMode, balance, updateBalance } = useUser();
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [recharging, setRecharging] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        loadBalance(),
        loadTransactionHistory()
      ]);
    } catch (error) {
      console.error('Error loading payment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBalance = async () => {
    try {
      const result = await paymentService.getBalance(user.id);
      if (result.success) {
        updateBalance(result.balance);
      }
    } catch (error) {
      console.error('Error loading balance:', error);
    }
  };

  const loadTransactionHistory = async () => {
    try {
      const result = await paymentService.getTransactionHistory(user.id);
      if (result.success) {
        setTransactions(result.transactions);
      }
    } catch (error) {
      console.error('Error loading transaction history:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleRecharge = async () => {
    Alert.alert(
      'Recargar Saldo',
      '¿Deseas recargar $10 a tu cuenta?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Recargar',
          onPress: async () => {
            setRecharging(true);
            try {
              const result = await paymentService.rechargeBalance(10);
              if (result.success) {
                updateBalance(result.newBalance);
                await loadTransactionHistory(); // Actualizar historial
                Alert.alert('Éxito', 'Saldo recargado correctamente');
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo recargar el saldo');
            } finally {
              setRecharging(false);
            }
          }
        }
      ]
    );
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'recharge':
        return 'add-circle';
      case 'commission':
        return 'remove-circle';
      case 'payment':
        return 'credit-card';
      default:
        return 'help';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'recharge':
        return '#4CAF50';
      case 'commission':
        return '#f44336';
      case 'payment':
        return '#2196F3';
      default:
        return '#666';
    }
  };

  const getTransactionTitle = (transaction) => {
    switch (transaction.type) {
      case 'recharge':
        return 'Recarga de saldo';
      case 'commission':
        return 'Comisión por viaje';
      case 'payment':
        return 'Pago recibido';
      default:
        return transaction.description || 'Transacción';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatAmount = (amount, type) => {
    const sign = type === 'recharge' ? '+' : '-';
    return `${sign}$${Math.abs(amount).toFixed(2)}`;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Cargando información de pagos...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Pagos y Saldo</Text>
        <Text style={styles.subtitle}>
          Gestiona tu dinero en Maclaren
        </Text>
      </View>

      {/* Balance Card */}
      <Card style={styles.balanceCard}>
        <Card.Content>
          <View style={styles.balanceHeader}>
            <MaterialIcons name="account-balance-wallet" size={32} color="#2196F3" />
            <Text style={styles.balanceTitle}>Saldo Actual</Text>
          </View>
          
          <Text style={styles.balanceAmount}>
            ${(balance || 0).toFixed(2)}
          </Text>

          {userMode === 'driver' && (
            <View style={styles.balanceStatus}>
              {(balance || 0) >= 10 ? (
                <Chip
                  icon="check-circle"
                  style={styles.statusChip}
                  textStyle={{ color: '#2e7d32' }}
                >
                  Listo para recibir viajes
                </Chip>
              ) : (
                <Chip
                  icon="warning"
                  style={[styles.statusChip, { backgroundColor: '#ffebee' }]}
                  textStyle={{ color: '#d32f2f' }}
                >
                  Saldo insuficiente
                </Chip>
              )}
            </View>
          )}

          <Button
            mode="contained"
            onPress={handleRecharge}
            loading={recharging}
            disabled={recharging}
            style={styles.rechargeButton}
            icon="credit-card"
          >
            Recargar $10
          </Button>
        </Card.Content>
      </Card>

      {/* Payment Methods Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Métodos de Pago</Text>
          
          <List.Item
            title="Tarjeta de Crédito"
            description="**** **** **** 1234"
            left={() => <MaterialIcons name="credit-card" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
          <Divider />
          
          <List.Item
            title="Agregar Método de Pago"
            description="Tarjeta, PayPal, etc."
            left={() => <MaterialIcons name="add-circle" size={24} color="#2196F3" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
        </Card.Content>
      </Card>

      {/* Transaction History */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Historial de Transacciones</Text>
          
          {transactions.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="receipt" size={48} color="#ccc" />
              <Text style={styles.emptyText}>Sin transacciones aún</Text>
            </View>
          ) : (
            transactions.map((transaction, index) => (
              <View key={transaction._id || index}>
                <List.Item
                  title={getTransactionTitle(transaction)}
                  description={formatDate(transaction.createdAt)}
                  left={() => (
                    <MaterialIcons
                      name={getTransactionIcon(transaction.type)}
                      size={24}
                      color={getTransactionColor(transaction.type)}
                    />
                  )}
                  right={() => (
                    <Text
                      style={[
                        styles.transactionAmount,
                        { color: getTransactionColor(transaction.type) }
                      ]}
                    >
                      {formatAmount(transaction.amount, transaction.type)}
                    </Text>
                  )}
                />
                {index < transactions.length - 1 && <Divider />}
              </View>
            ))
          )}
        </Card.Content>
      </Card>

      {/* Driver Info */}
      {userMode === 'driver' && (
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Información para Conductores</Text>
            
            <View style={styles.infoContainer}>
              <MaterialIcons name="info" size={20} color="#2196F3" />
              <Text style={styles.infoText}>
                Se cobra una comisión del 10% por cada viaje completado.
              </Text>
            </View>
            
            <View style={styles.infoContainer}>
              <MaterialIcons name="account-balance-wallet" size={20} color="#2196F3" />
              <Text style={styles.infoText}>
                Necesitas mantener al menos $10 de saldo para recibir viajes.
              </Text>
            </View>
            
            <View style={styles.infoContainer}>
              <MaterialIcons name="autorenew" size={20} color="#2196F3" />
              <Text style={styles.infoText}>
                Cuando tu saldo sea menor a $10, deberás recargar para continuar.
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    marginTop: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  balanceCard: {
    marginBottom: 16,
    elevation: 4,
    backgroundColor: '#fff',
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  balanceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 12,
    color: '#333',
  },
  balanceAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2196F3',
    textAlign: 'center',
    marginBottom: 16,
  },
  balanceStatus: {
    alignItems: 'center',
    marginBottom: 16,
  },
  statusChip: {
    backgroundColor: '#e8f5e8',
  },
  rechargeButton: {
    marginTop: 8,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  infoText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default PaymentScreen;
