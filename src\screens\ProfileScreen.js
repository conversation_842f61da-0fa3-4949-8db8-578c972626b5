import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Card, Text, Button, Avatar, List, Divider, Switch } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useUser } from '../context/UserContext';
import UserSwitcher from '../components/UserSwitcher';

const ProfileScreen = ({ navigation }) => {
  const { user, logout } = useAuth();
  const { userMode, balance } = useUser();
  const [loading, setLoading] = useState(false);

  const handleLogout = () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro que deseas cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Cerrar Sesión',
          onPress: async () => {
            setLoading(true);
            await logout();
            setLoading(false);
          }
        }
      ]
    );
  };

  const getInitials = () => {
    if (!user?.firstName || !user?.lastName) return 'U';
    return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
  };

  const getUserStats = () => {
    // En una implementación real, estos datos vendrían del backend
    return {
      totalTrips: 0,
      rating: 0,
      totalEarnings: 0,
    };
  };

  const stats = getUserStats();

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {/* Header Card */}
      <Card style={styles.headerCard}>
        <Card.Content style={styles.headerContent}>
          <Avatar.Text 
            size={80} 
            label={getInitials()}
            style={styles.avatar}
          />
          <View style={styles.userInfo}>
            <Text style={styles.userName}>
              {user?.firstName} {user?.lastName}
            </Text>
            <Text style={styles.userEmail}>{user?.email}</Text>
            <Text style={styles.userMode}>
              Modo: {userMode === 'driver' ? 'Conductor' : 'Pasajero'}
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Stats Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Estadísticas</Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <MaterialIcons name="directions-car" size={24} color="#2196F3" />
              <Text style={styles.statNumber}>{stats.totalTrips}</Text>
              <Text style={styles.statLabel}>Viajes</Text>
            </View>
            
            <View style={styles.statItem}>
              <MaterialIcons name="star" size={24} color="#FFC107" />
              <Text style={styles.statNumber}>
                {stats.rating > 0 ? stats.rating.toFixed(1) : 'N/A'}
              </Text>
              <Text style={styles.statLabel}>Calificación</Text>
            </View>
            
            {userMode === 'driver' && (
              <View style={styles.statItem}>
                <MaterialIcons name="account-balance-wallet" size={24} color="#4CAF50" />
                <Text style={styles.statNumber}>${(balance || 0).toFixed(2)}</Text>
                <Text style={styles.statLabel}>Saldo</Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>

      {/* User Switcher */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>🔄 Cambio de Usuario</Text>
          <Text style={styles.cardSubtitle}>
            Cambia entre Ana Pasajero y Carlos Conductor
          </Text>
          <UserSwitcher style={styles.userSwitcher} />
        </Card.Content>
      </Card>

      {/* Account Settings */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Configuración de Cuenta</Text>
          
          <List.Item
            title="Información Personal"
            description="Actualizar datos personales"
            left={() => <MaterialIcons name="person" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              // Navegar a pantalla de edición de perfil
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
          <Divider />
          
          <List.Item
            title="Documentos"
            description="Ver estado de verificación"
            left={() => <MaterialIcons name="description" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
          <Divider />
          
          <List.Item
            title="Métodos de Pago"
            description="Gestionar tarjetas y pagos"
            left={() => <MaterialIcons name="credit-card" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => navigation.navigate('Payment')}
          />
        </Card.Content>
      </Card>

      {/* App Settings */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Configuración de la App</Text>
          
          <List.Item
            title="Notificaciones"
            description="Gestionar notificaciones"
            left={() => <MaterialIcons name="notifications" size={24} color="#666" />}
            right={() => <Switch value={true} onValueChange={() => {}} />}
          />
          <Divider />
          
          <List.Item
            title="Ubicación"
            description="Permisos de ubicación"
            left={() => <MaterialIcons name="location-on" size={24} color="#666" />}
            right={() => <Switch value={true} onValueChange={() => {}} />}
          />
          <Divider />
          
          <List.Item
            title="Modo Oscuro"
            description="Cambiar tema de la aplicación"
            left={() => <MaterialIcons name="dark-mode" size={24} color="#666" />}
            right={() => <Switch value={false} onValueChange={() => {}} />}
          />
        </Card.Content>
      </Card>

      {/* Support */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Soporte</Text>
          
          <List.Item
            title="Centro de Ayuda"
            description="Preguntas frecuentes y guías"
            left={() => <MaterialIcons name="help" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
          <Divider />
          
          <List.Item
            title="Contactar Soporte"
            description="Obtener ayuda personalizada"
            left={() => <MaterialIcons name="support-agent" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
          <Divider />
          
          <List.Item
            title="Términos y Condiciones"
            description="Leer términos de uso"
            left={() => <MaterialIcons name="gavel" size={24} color="#666" />}
            right={() => <MaterialIcons name="chevron-right" size={24} color="#666" />}
            onPress={() => {
              Alert.alert('Próximamente', 'Esta función estará disponible pronto');
            }}
          />
        </Card.Content>
      </Card>

      {/* Logout Button */}
      <Button
        mode="contained"
        onPress={handleLogout}
        loading={loading}
        disabled={loading}
        style={styles.logoutButton}
        buttonColor="#f44336"
        icon="logout"
      >
        Cerrar Sesión
      </Button>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  headerCard: {
    marginBottom: 16,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: '#2196F3',
  },
  userInfo: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  userMode: {
    fontSize: 14,
    color: '#2196F3',
    marginTop: 4,
    fontWeight: '500',
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  userSwitcher: {
    marginTop: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  logoutButton: {
    marginTop: 16,
    marginBottom: 32,
  },
});

export default ProfileScreen;
