import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, StatusBar, Dimensions } from 'react-native';
import { TextInput, Button, Text, Card, RadioButton, Avatar, Chip } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';

const { width, height } = Dimensions.get('window');

const RegisterScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    address: '',
    userType: 'passenger', // 'passenger' or 'driver'
  });
  const [loading, setLoading] = useState(false);
  const { register } = useAuth();

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const { firstName, lastName, email, password, confirmPassword, phone, address } = formData;
    
    if (!firstName || !lastName || !email || !password || !phone || !address) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return false;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return false;
    }

    if (password.length < 6) {
      Alert.alert('Error', 'La contraseña debe tener al menos 6 caracteres');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Por favor ingresa un correo válido');
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    const result = await register(formData);
    setLoading(false);

    if (result.success) {
      Alert.alert(
        'Registro Exitoso',
        'Tu cuenta ha sido creada. Ahora debes subir tus documentos para verificación.',
        [
          {
            text: 'Continuar',
            onPress: () => navigation.navigate('DocumentUpload', { userData: formData })
          }
        ]
      );
    } else {
      Alert.alert('Error', result.error);
    }
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradientContainer}
      >
        <ScrollView contentContainerStyle={styles.container}>
          {/* Header mejorado */}
          <View style={styles.header}>
            <Avatar.Icon
              size={80}
              icon="account-plus"
              style={styles.avatar}
              theme={{ colors: { primary: '#ff6b6b' } }}
            />
            <Text style={styles.title}>Únete a Maclaren</Text>
            <Text style={styles.subtitle}>Crea tu cuenta y comienza a viajar 🚀</Text>
          </View>

          {/* Card principal */}
          <Card style={styles.card} elevation={8}>
            <Card.Content style={styles.cardContent}>
              <Text style={styles.formTitle}>Información Personal 👤</Text>

              <View style={styles.inputRow}>
                <TextInput
                  label="Nombre"
                  value={formData.firstName}
                  onChangeText={(value) => handleInputChange('firstName', value)}
                  mode="outlined"
                  style={[styles.input, styles.halfInput]}
                  left={<TextInput.Icon icon="account" />}
                  theme={{ colors: { primary: '#ff6b6b' } }}
                />
                <TextInput
                  label="Apellido"
                  value={formData.lastName}
                  onChangeText={(value) => handleInputChange('lastName', value)}
                  mode="outlined"
                  style={[styles.input, styles.halfInput]}
                  theme={{ colors: { primary: '#ff6b6b' } }}
                />
              </View>

              <TextInput
                label="Correo electrónico"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                style={styles.input}
                left={<TextInput.Icon icon="email" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <TextInput
                label="Teléfono"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                mode="outlined"
                keyboardType="phone-pad"
                style={styles.input}
                left={<TextInput.Icon icon="phone" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <TextInput
                label="Dirección"
                value={formData.address}
                onChangeText={(value) => handleInputChange('address', value)}
                mode="outlined"
                multiline
                style={styles.input}
                left={<TextInput.Icon icon="map-marker" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <Text style={styles.formTitle}>Seguridad 🔒</Text>

              <TextInput
                label="Contraseña"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                mode="outlined"
                secureTextEntry
                style={styles.input}
                left={<TextInput.Icon icon="lock" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <TextInput
                label="Confirmar contraseña"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                mode="outlined"
                secureTextEntry
                style={styles.input}
                left={<TextInput.Icon icon="lock-check" />}
                theme={{ colors: { primary: '#ff6b6b' } }}
              />

              <Text style={styles.formTitle}>Tipo de Usuario 🚗👤</Text>
              <View style={styles.userTypeContainer}>
                <Chip
                  selected={formData.userType === 'passenger'}
                  onPress={() => handleInputChange('userType', 'passenger')}
                  style={[
                    styles.userTypeChip,
                    formData.userType === 'passenger' && styles.selectedChip
                  ]}
                  textStyle={formData.userType === 'passenger' ? styles.selectedChipText : styles.chipText}
                  icon="account"
                >
                  Pasajero 🚶‍♂️
                </Chip>
                <Chip
                  selected={formData.userType === 'driver'}
                  onPress={() => handleInputChange('userType', 'driver')}
                  style={[
                    styles.userTypeChip,
                    formData.userType === 'driver' && styles.selectedChip
                  ]}
                  textStyle={formData.userType === 'driver' ? styles.selectedChipText : styles.chipText}
                  icon="car"
                >
                  Conductor 🚗
                </Chip>
              </View>

              <Button
                mode="contained"
                onPress={handleRegister}
                loading={loading}
                disabled={loading}
                style={styles.button}
                buttonColor="#ff6b6b"
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}
              >
                {loading ? 'Creando cuenta...' : 'Crear Cuenta ✨'}
              </Button>

              <Button
                mode="text"
                onPress={() => navigation.navigate('Login')}
                style={styles.linkButton}
                textColor="#ff6b6b"
              >
                ¿Ya tienes cuenta? Inicia sesión aquí 👋
              </Button>
            </Card.Content>
          </Card>
        </ScrollView>
      </LinearGradient>
    </>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
    minHeight: height,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    paddingTop: 20,
  },
  avatar: {
    marginBottom: 20,
    backgroundColor: '#ff6b6b',
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    fontWeight: '300',
  },
  card: {
    marginHorizontal: 10,
    borderRadius: 20,
    backgroundColor: 'white',
    marginBottom: 20,
  },
  cardContent: {
    padding: 25,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    marginTop: 10,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  input: {
    marginBottom: 16,
    backgroundColor: 'white',
  },
  halfInput: {
    width: '48%',
  },
  userTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  userTypeChip: {
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 10,
  },
  selectedChip: {
    backgroundColor: '#ff6b6b',
  },
  chipText: {
    color: '#666',
  },
  selectedChipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  button: {
    marginTop: 25,
    paddingVertical: 8,
    borderRadius: 12,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  linkButton: {
    marginTop: 20,
  },
});

export default RegisterScreen;
