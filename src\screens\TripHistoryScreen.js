import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import { Card, Text, Chip, List, Divider, ActivityIndicator } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useUser } from '../context/UserContext';
import { tripService } from '../services/tripService';

const TripHistoryScreen = () => {
  const { user } = useAuth();
  const { userMode } = useUser();
  const [trips, setTrips] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadTripHistory();
  }, []);

  const loadTripHistory = async () => {
    try {
      const result = await tripService.getTripHistory(user.id);
      if (result.success) {
        setTrips(result.trips);
      }
    } catch (error) {
      console.error('Error loading trip history:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTripHistory();
    setRefreshing(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return '#f44336';
      case 'in_progress':
        return '#2196F3';
      case 'pending':
        return '#ff9800';
      default:
        return '#666';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completado';
      case 'cancelled':
        return 'Cancelado';
      case 'in_progress':
        return 'En Progreso';
      case 'pending':
        return 'Pendiente';
      default:
        return status;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatAddress = (location) => {
    if (location.address) {
      return location.address;
    }
    return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
  };

  const getTripIcon = (status) => {
    switch (status) {
      case 'completed':
        return 'check-circle';
      case 'cancelled':
        return 'cancel';
      case 'in_progress':
        return 'directions-car';
      case 'pending':
        return 'schedule';
      default:
        return 'help';
    }
  };

  const renderTripItem = (trip) => (
    <Card key={trip._id} style={styles.tripCard}>
      <Card.Content>
        <View style={styles.tripHeader}>
          <View style={styles.tripInfo}>
            <Text style={styles.tripDate}>
              {formatDate(trip.createdAt)}
            </Text>
            <Text style={styles.tripPrice}>
              ${trip.price.toFixed(2)}
            </Text>
          </View>
          <Chip
            icon={getTripIcon(trip.status)}
            style={[styles.statusChip, { backgroundColor: getStatusColor(trip.status) + '20' }]}
            textStyle={{ color: getStatusColor(trip.status) }}
          >
            {getStatusText(trip.status)}
          </Chip>
        </View>

        <Divider style={styles.divider} />

        <List.Item
          title="Origen"
          description={formatAddress(trip.pickupLocation)}
          left={() => <MaterialIcons name="my-location" size={20} color="#4CAF50" />}
          titleStyle={styles.locationTitle}
          descriptionStyle={styles.locationDescription}
        />

        <List.Item
          title="Destino"
          description={formatAddress(trip.destination)}
          left={() => <MaterialIcons name="location-on" size={20} color="#f44336" />}
          titleStyle={styles.locationTitle}
          descriptionStyle={styles.locationDescription}
        />

        {trip.driver && userMode === 'passenger' && (
          <>
            <Divider style={styles.divider} />
            <List.Item
              title="Conductor"
              description={`${trip.driver.firstName} ${trip.driver.lastName}`}
              left={() => <MaterialIcons name="person" size={20} color="#2196F3" />}
              titleStyle={styles.locationTitle}
              descriptionStyle={styles.locationDescription}
            />
          </>
        )}

        {trip.passenger && userMode === 'driver' && (
          <>
            <Divider style={styles.divider} />
            <List.Item
              title="Pasajero"
              description={`${trip.passenger.firstName} ${trip.passenger.lastName}`}
              left={() => <MaterialIcons name="person" size={20} color="#2196F3" />}
              titleStyle={styles.locationTitle}
              descriptionStyle={styles.locationDescription}
            />
          </>
        )}

        {trip.notes && (
          <>
            <Divider style={styles.divider} />
            <List.Item
              title="Notas"
              description={trip.notes}
              left={() => <MaterialIcons name="note" size={20} color="#666" />}
              titleStyle={styles.locationTitle}
              descriptionStyle={styles.locationDescription}
            />
          </>
        )}

        {trip.commission && userMode === 'driver' && (
          <View style={styles.commissionContainer}>
            <Text style={styles.commissionText}>
              Comisión: ${trip.commission.toFixed(2)} (10%)
            </Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Cargando historial...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Historial de Viajes</Text>
        <Text style={styles.subtitle}>
          Modo: {userMode === 'driver' ? 'Conductor' : 'Pasajero'}
        </Text>
      </View>

      {trips.length === 0 ? (
        <Card style={styles.emptyCard}>
          <Card.Content style={styles.emptyContent}>
            <MaterialIcons name="history" size={64} color="#ccc" />
            <Text style={styles.emptyTitle}>Sin viajes aún</Text>
            <Text style={styles.emptyDescription}>
              {userMode === 'driver' 
                ? 'Cuando aceptes viajes aparecerán aquí'
                : 'Cuando solicites viajes aparecerán aquí'
              }
            </Text>
          </Card.Content>
        </Card>
      ) : (
        <>
          <View style={styles.statsContainer}>
            <Card style={styles.statCard}>
              <Card.Content style={styles.statContent}>
                <Text style={styles.statNumber}>{trips.length}</Text>
                <Text style={styles.statLabel}>Total Viajes</Text>
              </Card.Content>
            </Card>
            
            <Card style={styles.statCard}>
              <Card.Content style={styles.statContent}>
                <Text style={styles.statNumber}>
                  {trips.filter(trip => trip.status === 'completed').length}
                </Text>
                <Text style={styles.statLabel}>Completados</Text>
              </Card.Content>
            </Card>
          </View>

          {trips.map(renderTripItem)}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    marginTop: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  tripCard: {
    marginBottom: 12,
    elevation: 2,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripInfo: {
    flex: 1,
  },
  tripDate: {
    fontSize: 14,
    color: '#666',
  },
  tripPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  statusChip: {
    marginLeft: 8,
  },
  divider: {
    marginVertical: 8,
  },
  locationTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  locationDescription: {
    fontSize: 12,
    color: '#666',
  },
  commissionContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#fff3e0',
    borderRadius: 4,
  },
  commissionText: {
    fontSize: 12,
    color: '#f57c00',
    textAlign: 'center',
  },
  emptyCard: {
    elevation: 2,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default TripHistoryScreen;
