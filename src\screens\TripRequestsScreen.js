import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Card,
  Button,
  Surface,
  Chip,
  Avatar,
  Divider,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useUser } from '../context/UserContext';
import { tripService } from '../services/tripService';
import { socketService } from '../services/socketService';

const { height } = Dimensions.get('window');

const TripRequestsScreen = ({ navigation }) => {
  const { currentTrip, startTrip } = useUser();
  const [pendingRequests, setPendingRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPendingRequests();
    
    // Configurar listeners para nuevas solicitudes
    socketService.addListener('driver-request-received', handleNewDriverRequest);
    
    return () => {
      socketService.removeListener('driver-request-received', handleNewDriverRequest);
    };
  }, []);

  const loadPendingRequests = async () => {
    if (!currentTrip) return;
    
    try {
      setLoading(true);
      // Simular solicitudes pendientes para demo
      const mockRequests = [
        {
          id: 'req-1',
          tripId: currentTrip._id,
          driver: {
            id: 'driver-1',
            firstName: 'Carlos',
            lastName: 'Rodríguez',
            phone: '+507 6123-4567',
            rating: 4.8,
            vehicle: {
              brand: 'Toyota',
              model: 'Corolla',
              plate: 'ABC-123',
              color: 'Blanco'
            },
            photo: null
          },
          requestedAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutos atrás
          status: 'pending'
        },
        {
          id: 'req-2',
          tripId: currentTrip._id,
          driver: {
            id: 'driver-2',
            firstName: 'María',
            lastName: 'González',
            phone: '+507 6987-6543',
            rating: 4.6,
            vehicle: {
              brand: 'Honda',
              model: 'Civic',
              plate: 'XYZ-789',
              color: 'Azul'
            },
            photo: null
          },
          requestedAt: new Date(Date.now() - 3 * 60 * 1000), // 3 minutos atrás
          status: 'pending'
        }
      ];
      
      setPendingRequests(mockRequests);
    } catch (error) {
      console.error('Error cargando solicitudes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNewDriverRequest = (data) => {
    console.log('📥 Nueva solicitud de conductor recibida:', data);
    
    if (currentTrip && currentTrip._id === data.tripId) {
      const newRequest = {
        id: `req-${Date.now()}`,
        tripId: data.tripId,
        driver: data.driverInfo,
        requestedAt: new Date(),
        status: 'pending'
      };
      
      setPendingRequests(prev => [newRequest, ...prev]);
      
      // Mostrar notificación
      Alert.alert(
        '🚗 Nueva Solicitud',
        `${data.driverInfo.firstName} ${data.driverInfo.lastName} quiere ser tu conductor.`,
        [
          { text: 'Ver después', style: 'cancel' },
          { text: 'Ver ahora', onPress: () => {} }
        ]
      );
    }
  };

  const handleAcceptDriver = async (request) => {
    Alert.alert(
      'Aceptar Conductor',
      `¿Confirmas que quieres que ${request.driver.firstName} ${request.driver.lastName} sea tu conductor?\n\nVehículo: ${request.driver.vehicle.brand} ${request.driver.vehicle.model}\nPlaca: ${request.driver.vehicle.plate}`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Confirmar',
          onPress: async () => {
            try {
              setLoading(true);
              
              const result = await tripService.passengerAcceptDriver(
                request.tripId, 
                request.driver.id
              );
              
              if (result.success) {
                // Actualizar el viaje actual
                startTrip(result.trip);

                // Notificar al conductor con información completa del viaje
                socketService.notifyPassengerResponse(
                  request.tripId,
                  request.driver.id,
                  true,
                  {
                    trip: result.trip,
                    passengerInfo: {
                      firstName: currentTrip.passenger?.firstName || 'Pasajero',
                      lastName: currentTrip.passenger?.lastName || '',
                      phone: currentTrip.passenger?.phone || '+507 0000-0000'
                    },
                    shouldOpenNavigation: true
                  }
                );

                // Limpiar solicitudes pendientes
                setPendingRequests([]);

                Alert.alert(
                  'Conductor Confirmado ✅',
                  `${request.driver.firstName} ${request.driver.lastName} es ahora tu conductor.\n\nSe le ha enviado tu ubicación y se abrirá la navegación automáticamente en su dispositivo.`,
                  [
                    {
                      text: 'Ver en Mapa',
                      onPress: () => navigation.navigate('Map')
                    }
                  ]
                );
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo confirmar el conductor');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleRejectDriver = async (request) => {
    Alert.alert(
      'Rechazar Conductor',
      `¿Estás seguro de que quieres rechazar a ${request.driver.firstName} ${request.driver.lastName}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Rechazar',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await tripService.rejectDriver(
                request.tripId, 
                request.driver.id
              );
              
              if (result.success) {
                // Notificar al conductor
                socketService.notifyPassengerResponse(
                  request.tripId, 
                  request.driver.id, 
                  false
                );
                
                // Remover de la lista
                setPendingRequests(prev => 
                  prev.filter(req => req.id !== request.id)
                );
                
                Alert.alert('Conductor Rechazado', 'El conductor ha sido notificado.');
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo rechazar al conductor');
            }
          }
        }
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPendingRequests();
    setRefreshing(false);
  };

  const formatTimeAgo = (date) => {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Ahora mismo';
    if (diffMins < 60) return `Hace ${diffMins} min`;
    
    const diffHours = Math.floor(diffMins / 60);
    return `Hace ${diffHours}h`;
  };

  if (!currentTrip) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="car-sport" size={64} color="#ccc" />
        <Text style={styles.emptyTitle}>No hay viaje activo</Text>
        <Text style={styles.emptySubtitle}>
          Crea un viaje primero para ver las solicitudes de conductores
        </Text>
        <Button
          mode="contained"
          onPress={() => navigation.navigate('Map')}
          style={styles.emptyButton}
          buttonColor="#ff6b6b"
        >
          Ir al Mapa
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <Surface style={styles.header} elevation={4}>
        <LinearGradient
          colors={['#ff6b6b', '#ee5a24']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <Ionicons name="people" size={28} color="white" />
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>Solicitudes de Conductores</Text>
              <Text style={styles.headerSubtitle}>
                {pendingRequests.length} solicitud(es) pendiente(s)
              </Text>
            </View>
            <Chip
              style={styles.statusChip}
              textStyle={styles.statusChipText}
            >
              ${currentTrip.offeredPrice}
            </Chip>
          </View>
        </LinearGradient>
      </Surface>

      {/* Lista de solicitudes */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {pendingRequests.length === 0 ? (
          <View style={styles.emptyRequestsContainer}>
            <Ionicons name="hourglass-outline" size={48} color="#ccc" />
            <Text style={styles.emptyRequestsTitle}>
              Esperando conductores...
            </Text>
            <Text style={styles.emptyRequestsSubtitle}>
              Los conductores cercanos verán tu solicitud y podrán enviar propuestas.
            </Text>
            <Button
              mode="outlined"
              onPress={onRefresh}
              style={styles.refreshButton}
              icon="refresh"
            >
              Actualizar
            </Button>
          </View>
        ) : (
          pendingRequests.map((request) => (
            <Card key={request.id} style={styles.requestCard} elevation={3}>
              <Card.Content style={styles.requestContent}>
                {/* Header del conductor */}
                <View style={styles.driverHeader}>
                  <Avatar.Icon
                    size={50}
                    icon="account"
                    style={styles.driverAvatar}
                  />
                  <View style={styles.driverInfo}>
                    <Text style={styles.driverName}>
                      {request.driver.firstName} {request.driver.lastName}
                    </Text>
                    <Text style={styles.driverPhone}>
                      {request.driver.phone}
                    </Text>
                    <View style={styles.ratingContainer}>
                      <Ionicons name="star" size={16} color="#FFD700" />
                      <Text style={styles.ratingText}>
                        {request.driver.rating}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.timeContainer}>
                    <Text style={styles.timeText}>
                      {formatTimeAgo(request.requestedAt)}
                    </Text>
                  </View>
                </View>

                <Divider style={styles.divider} />

                {/* Información del vehículo */}
                <View style={styles.vehicleInfo}>
                  <Ionicons name="car-sport" size={20} color="#4CAF50" />
                  <Text style={styles.vehicleText}>
                    {request.driver.vehicle.brand} {request.driver.vehicle.model} - {request.driver.vehicle.plate}
                  </Text>
                  {request.driver.vehicle.color && (
                    <Chip
                      style={styles.colorChip}
                      textStyle={styles.colorChipText}
                      compact
                    >
                      {request.driver.vehicle.color}
                    </Chip>
                  )}
                </View>

                {/* Botones de acción */}
                <View style={styles.actionButtons}>
                  <Button
                    mode="outlined"
                    onPress={() => handleRejectDriver(request)}
                    style={styles.rejectButton}
                    textColor="#f44336"
                    icon="close"
                    disabled={loading}
                  >
                    Rechazar
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => handleAcceptDriver(request)}
                    style={styles.acceptButton}
                    buttonColor="#4CAF50"
                    icon="check"
                    loading={loading}
                    disabled={loading}
                  >
                    Aceptar
                  </Button>
                </View>
              </Card.Content>
            </Card>
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: 40,
  },
  headerGradient: {
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 2,
  },
  statusChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  statusChipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  requestCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  requestContent: {
    padding: 16,
  },
  driverHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  driverAvatar: {
    backgroundColor: '#4CAF50',
  },
  driverInfo: {
    flex: 1,
    marginLeft: 12,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  driverPhone: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 4,
  },
  timeContainer: {
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  divider: {
    marginVertical: 12,
  },
  vehicleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  vehicleText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  colorChip: {
    height: 24,
    backgroundColor: '#e3f2fd',
  },
  colorChipText: {
    fontSize: 12,
    color: '#1976d2',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  rejectButton: {
    flex: 1,
    borderColor: '#f44336',
  },
  acceptButton: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#f5f5f5',
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 24,
  },
  emptyButton: {
    marginTop: 24,
    borderRadius: 12,
  },
  emptyRequestsContainer: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 32,
  },
  emptyRequestsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyRequestsSubtitle: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  refreshButton: {
    marginTop: 16,
    borderRadius: 8,
  },
});

export default TripRequestsScreen;
