import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Avatar,
  Divider,
  Chip,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../context/AuthContext';
import { OfflineStorage } from '../config/api';

const UserSelectionScreen = ({ navigation }) => {
  const { login } = useAuth();

  const handleUserLogin = async (userType, email) => {
    try {
      console.log(`🔄 Iniciando sesión como ${userType}`);
      
      const result = await login(email, 'demo123');
      
      if (result.success) {
        console.log(`✅ Login exitoso como ${userType}`);
        // La navegación se maneja automáticamente por el AuthContext
      } else {
        Alert.alert('Error', result.error || 'Error al iniciar sesión');
      }
    } catch (error) {
      console.error('Error en login:', error);
      Alert.alert('Error', 'Error inesperado al iniciar sesión');
    }
  };

  const clearAllTrips = async () => {
    Alert.alert(
      'Limpiar Viajes',
      '¿Estás seguro de que quieres eliminar todos los viajes creados localmente?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            await OfflineStorage.clearAllTrips();
            Alert.alert('Éxito', 'Todos los viajes han sido eliminados');
          },
        },
      ]
    );
  };

  const showCurrentTrips = async () => {
    try {
      const createdTrips = await OfflineStorage.getCreatedTrips();
      const availableTrips = await OfflineStorage.getAvailableTrips();

      const message = `
📊 Estado actual de viajes:

🆕 Viajes creados por usuarios: ${createdTrips.length}
📋 Total viajes disponibles: ${availableTrips.length}

${createdTrips.length > 0 ?
  '🚗 Últimos viajes creados:\n' +
  createdTrips.slice(-3).map(trip =>
    `• ${trip.pickupLocation.address} → ${trip.destination.address} ($${trip.offeredPrice})`
  ).join('\n')
  : ''}
      `.trim();

      Alert.alert('Estado de Viajes', message);
    } catch (error) {
      Alert.alert('Error', 'No se pudo obtener información de viajes');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Avatar.Icon 
            size={80} 
            icon="car" 
            style={styles.logo}
          />
          <Title style={styles.title}>🚗 Maclaren</Title>
          <Paragraph style={styles.subtitle}>
            Selecciona el tipo de usuario para probar la aplicación
          </Paragraph>
        </View>

        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.userOption}>
              <Avatar.Icon 
                size={60} 
                icon="account" 
                style={styles.passengerAvatar}
              />
              <View style={styles.userInfo}>
                <Title style={styles.userTitle}>👤 Ana Pasajero</Title>
                <Paragraph style={styles.userDescription}>
                  • Crear solicitudes de viaje{'\n'}
                  • Ver historial de viajes{'\n'}
                  • Gestionar saldo: $50,000 COP
                </Paragraph>
                <Chip icon="account" style={styles.chip}>
                  Pasajero
                </Chip>
              </View>
            </View>
            <Button
              mode="contained"
              onPress={() => handleUserLogin('pasajero', '<EMAIL>')}
              style={styles.loginButton}
              icon="login"
            >
              Entrar como Pasajero
            </Button>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.userOption}>
              <Avatar.Icon 
                size={60} 
                icon="car" 
                style={styles.driverAvatar}
              />
              <View style={styles.userInfo}>
                <Title style={styles.userTitle}>🚗 Carlos Conductor</Title>
                <Paragraph style={styles.userDescription}>
                  • Ver viajes disponibles{'\n'}
                  • Aceptar y gestionar viajes{'\n'}
                  • Gestionar saldo: $75,000 COP{'\n'}
                  • Vehículo: Toyota Corolla ABC-123
                </Paragraph>
                <Chip icon="car" style={styles.chip}>
                  Conductor
                </Chip>
              </View>
            </View>
            <Button
              mode="contained"
              onPress={() => handleUserLogin('conductor', '<EMAIL>')}
              style={styles.loginButton}
              icon="login"
            >
              Entrar como Conductor
            </Button>
          </Card.Content>
        </Card>

        <Divider style={styles.divider} />

        <Card style={[styles.card, styles.adminCard]}>
          <Card.Content>
            <Title style={styles.adminTitle}>🛠️ Herramientas de Testing</Title>
            <Paragraph style={styles.adminDescription}>
              Utilidades para probar la aplicación
            </Paragraph>
            <View style={styles.adminButtons}>
              <Button
                mode="outlined"
                onPress={showCurrentTrips}
                style={[styles.adminButton, { marginBottom: 10 }]}
                icon="information"
              >
                Ver Estado de Viajes
              </Button>
              <Button
                mode="outlined"
                onPress={clearAllTrips}
                style={styles.adminButton}
                icon="delete"
              >
                Limpiar Todos los Viajes
              </Button>
            </View>
          </Card.Content>
        </Card>

        <View style={styles.footer}>
          <Paragraph style={styles.footerText}>
            💡 Tip: Crea un viaje como pasajero, luego cambia a conductor para verlo
          </Paragraph>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    backgroundColor: '#1a1a2e',
    marginBottom: 15,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1a1a2e',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginHorizontal: 20,
  },
  card: {
    marginBottom: 20,
    elevation: 4,
  },
  userOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  passengerAvatar: {
    backgroundColor: '#4CAF50',
    marginRight: 15,
  },
  driverAvatar: {
    backgroundColor: '#2196F3',
    marginRight: 15,
  },
  userInfo: {
    flex: 1,
  },
  userTitle: {
    fontSize: 20,
    marginBottom: 8,
  },
  userDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  chip: {
    alignSelf: 'flex-start',
  },
  loginButton: {
    marginTop: 10,
  },
  divider: {
    marginVertical: 20,
  },
  adminCard: {
    backgroundColor: '#fff3e0',
  },
  adminTitle: {
    color: '#f57c00',
    marginBottom: 10,
  },
  adminDescription: {
    color: '#666',
    marginBottom: 15,
  },
  adminButtons: {
    gap: 10,
  },
  adminButton: {
    borderColor: '#f57c00',
  },
  footer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
  },
  footerText: {
    textAlign: 'center',
    color: '#1976d2',
    fontStyle: 'italic',
  },
});

export default UserSelectionScreen;
