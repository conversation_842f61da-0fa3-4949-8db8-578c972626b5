import { createApiInstance, apiRequest, OFFLINE_DATA } from '../config/api';

const api = createApiInstance();

export const authService = {
  async login(email, password) {
    // Determinar tipo de usuario basado en el email
    let offlineUser;
    let tokenSuffix;

    if (email.includes('conductor') || email.includes('driver')) {
      offlineUser = OFFLINE_DATA.driverUser;
      tokenSuffix = 'driver';
      console.log('🚗 Login como conductor');
    } else {
      offlineUser = OFFLINE_DATA.passengerUser;
      tokenSuffix = 'passenger';
      console.log('👤 Login como pasajero');
    }

    const result = await apiRequest(
      () => api.post('/auth/login', { email, password }),
      {
        token: `offline-token-${tokenSuffix}-${Date.now()}`,
        user: {
          ...offlineUser,
          email: email,
        },
      },
      'Error en login'
    );

    if (result.success) {
      if (result.isOffline) {
        console.log(`🔄 Login offline exitoso como ${result.data.user.userType}`);
      }
      return {
        success: true,
        token: result.data.token,
        user: result.data.user,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async register(userData) {
    const result = await apiRequest(
      () => api.post('/auth/register', userData),
      {
        message: 'Usuario registrado exitosamente (offline)',
        userId: 'offline-user-' + Date.now(),
      },
      'Error en registro'
    );

    if (result.success) {
      return {
        success: true,
        message: result.data.message,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async uploadDocument(documentData) {
    // En modo offline, simular subida exitosa
    console.log('📄 Simulando subida de documentos (offline)');

    return {
      success: true,
      message: 'Documentos subidos exitosamente (offline)',
      isOffline: true,
    };
  },

  async verifyDocuments(userId) {
    const result = await apiRequest(
      () => api.get(`/auth/verify-documents/${userId}`),
      {
        verified: true,
        documents: {
          id: true,
          license: true,
          vehicleRegistration: true,
          insurance: true,
        },
      },
      'Error al verificar documentos'
    );

    if (result.success) {
      return {
        success: true,
        verified: result.data.verified,
        documents: result.data.documents,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },
};
