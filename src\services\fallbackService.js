import AsyncStorage from '@react-native-async-storage/async-storage';

class FallbackService {
  constructor() {
    this.listeners = new Map();
    this.pollingInterval = null;
    this.isPolling = false;
  }

  // Crear un viaje
  async createTrip(tripData) {
    try {
      console.log('🔄 Usando servicio de respaldo para crear viaje:', tripData);
      
      const trip = {
        ...tripData,
        id: `trip-${Date.now()}`,
        status: 'pending',
        createdAt: new Date().toISOString()
      };
      
      // Guardar el viaje
      await AsyncStorage.setItem(`fallback_trip_${trip.id}`, JSON.stringify(trip));
      
      // Actualizar lista de viajes activos
      const activeTrips = await this.getActiveTrips();
      activeTrips.push(trip);
      await AsyncStorage.setItem('fallback_active_trips', JSON.stringify(activeTrips));
      
      console.log('✅ Viaje creado con servicio de respaldo:', trip.id);
      
      return {
        success: true,
        trip: trip
      };
    } catch (error) {
      console.error('❌ Error en servicio de respaldo:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Obtener viajes activos
  async getActiveTrips() {
    try {
      const activeTripsStr = await AsyncStorage.getItem('fallback_active_trips');
      return activeTripsStr ? JSON.parse(activeTripsStr) : [];
    } catch (error) {
      console.error('❌ Error obteniendo viajes activos:', error);
      return [];
    }
  }

  // Enviar solicitud de conductor (simulado)
  sendDriverRequest(tripId, driverInfo) {
    try {
      console.log('🚗 Simulando solicitud de conductor:', { tripId, driverInfo });
      
      const request = {
        id: `request-${Date.now()}`,
        tripId: tripId,
        driverInfo: driverInfo,
        timestamp: new Date().toISOString()
      };
      
      // Simular notificación después de 2 segundos
      setTimeout(() => {
        this.notifyListeners('driver-request-received', request);
      }, 2000);
      
      console.log('✅ Solicitud simulada enviada');
      return true;
    } catch (error) {
      console.error('❌ Error simulando solicitud:', error);
      return false;
    }
  }

  // Responder a solicitud de conductor (simulado)
  respondToDriverRequest(tripId, driverId, accepted, additionalData = {}) {
    try {
      console.log('📤 Simulando respuesta del pasajero:', { tripId, driverId, accepted });
      
      const response = {
        tripId: tripId,
        driverId: driverId,
        accepted: accepted,
        ...additionalData,
        timestamp: new Date().toISOString()
      };
      
      // Simular respuesta después de 1 segundo
      setTimeout(() => {
        this.notifyListeners('passenger-response', response);
      }, 1000);
      
      console.log('✅ Respuesta simulada enviada');
      return true;
    } catch (error) {
      console.error('❌ Error simulando respuesta:', error);
      return false;
    }
  }

  // Agregar listener
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
    console.log('👂 Listener agregado para:', event);
  }

  // Remover listener
  removeListener(event, callback) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
    console.log('🗑️ Listener removido para:', event);
  }

  // Notificar listeners
  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error en listener:', error);
        }
      });
    }
  }

  // Verificar si está "conectado" (siempre true para fallback)
  isSocketConnected() {
    return true;
  }

  // Conectar (simulado)
  async connect(userData) {
    console.log('🔄 Usando servicio de respaldo (sin servidor real)');
    console.log('👤 Datos de usuario:', userData);
    
    // Simular conexión exitosa
    setTimeout(() => {
      console.log('✅ Servicio de respaldo "conectado"');
    }, 1000);
    
    return true;
  }

  // Desconectar (simulado)
  disconnect() {
    console.log('👋 Desconectando servicio de respaldo...');
    this.listeners.clear();
  }

  // Limpiar datos
  async cleanup() {
    console.log('🧹 Limpiando datos del servicio de respaldo...');
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const fallbackKeys = allKeys.filter(key => key.startsWith('fallback_'));
      await AsyncStorage.multiRemove(fallbackKeys);
      console.log('✅ Datos del servicio de respaldo limpiados');
    } catch (error) {
      console.error('❌ Error limpiando datos:', error);
    }
  }
}

// Exportar instancia singleton
export const fallbackService = new FallbackService();
export default fallbackService;
