import { database } from '../config/firebase';
import { ref, push, set, onValue, off, remove } from 'firebase/database';

class FirebaseService {
  constructor() {
    this.listeners = new Map();
  }

  // Crear un viaje en Firebase
  async createTrip(tripData) {
    try {
      console.log('🔥 Creando viaje en Firebase:', tripData);
      
      const tripsRef = ref(database, 'trips');
      const newTripRef = push(tripsRef);
      
      const trip = {
        ...tripData,
        id: newTripRef.key,
        status: 'pending',
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      
      await set(newTripRef, trip);
      console.log('✅ Viaje creado en Firebase:', trip.id);
      
      return {
        success: true,
        trip: trip
      };
    } catch (error) {
      console.error('❌ Error creando viaje en Firebase:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Enviar solicitud de conductor
  async sendDriverRequest(tripId, driverInfo) {
    try {
      console.log('🚗 Enviando solicitud de conductor a Firebase:', { tripId, driverInfo });
      
      const requestsRef = ref(database, `driver_requests/${tripId}`);
      const newRequestRef = push(requestsRef);
      
      const request = {
        id: newRequestRef.key,
        tripId: tripId,
        driverInfo: driverInfo,
        status: 'pending',
        timestamp: Date.now()
      };
      
      await set(newRequestRef, request);
      console.log('✅ Solicitud de conductor enviada a Firebase:', request.id);
      
      return {
        success: true,
        request: request
      };
    } catch (error) {
      console.error('❌ Error enviando solicitud de conductor:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Escuchar solicitudes de conductores (para pasajeros)
  listenToDriverRequests(tripId, callback) {
    try {
      console.log('👂 Escuchando solicitudes de conductores para viaje:', tripId);
      
      const requestsRef = ref(database, `driver_requests/${tripId}`);
      
      const unsubscribe = onValue(requestsRef, (snapshot) => {
        const data = snapshot.val();
        const requests = data ? Object.values(data) : [];
        console.log('📥 Solicitudes de conductores recibidas:', requests.length);
        callback(requests);
      });
      
      this.listeners.set(`driver_requests_${tripId}`, unsubscribe);
      
      return () => {
        off(requestsRef);
        this.listeners.delete(`driver_requests_${tripId}`);
      };
    } catch (error) {
      console.error('❌ Error escuchando solicitudes de conductores:', error);
      return () => {};
    }
  }

  // Responder a solicitud de conductor (pasajero acepta/rechaza)
  async respondToDriverRequest(tripId, requestId, accepted, additionalData = {}) {
    try {
      console.log('📤 Enviando respuesta del pasajero:', { tripId, requestId, accepted });
      
      const responseRef = ref(database, `passenger_responses/${tripId}_${requestId}`);
      
      const response = {
        tripId: tripId,
        requestId: requestId,
        accepted: accepted,
        timestamp: Date.now(),
        ...additionalData
      };
      
      await set(responseRef, response);
      
      // Si fue aceptado, limpiar otras solicitudes
      if (accepted) {
        const requestsRef = ref(database, `driver_requests/${tripId}`);
        await remove(requestsRef);
      }
      
      console.log('✅ Respuesta del pasajero enviada a Firebase');
      
      return {
        success: true,
        response: response
      };
    } catch (error) {
      console.error('❌ Error enviando respuesta del pasajero:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Escuchar respuestas del pasajero (para conductores)
  listenToPassengerResponses(tripId, requestId, callback) {
    try {
      console.log('👂 Escuchando respuesta del pasajero para:', { tripId, requestId });
      
      const responseRef = ref(database, `passenger_responses/${tripId}_${requestId}`);
      
      const unsubscribe = onValue(responseRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          console.log('📥 Respuesta del pasajero recibida:', data);
          callback(data);
        }
      });
      
      this.listeners.set(`passenger_response_${tripId}_${requestId}`, unsubscribe);
      
      return () => {
        off(responseRef);
        this.listeners.delete(`passenger_response_${tripId}_${requestId}`);
      };
    } catch (error) {
      console.error('❌ Error escuchando respuesta del pasajero:', error);
      return () => {};
    }
  }

  // Obtener viajes disponibles
  async getAvailableTrips() {
    try {
      console.log('🔍 Obteniendo viajes disponibles de Firebase...');
      
      return new Promise((resolve) => {
        const tripsRef = ref(database, 'trips');
        
        onValue(tripsRef, (snapshot) => {
          const data = snapshot.val();
          const trips = data ? Object.values(data).filter(trip => trip.status === 'pending') : [];
          console.log('📋 Viajes disponibles encontrados:', trips.length);
          
          resolve({
            success: true,
            trips: trips
          });
        }, { onlyOnce: true });
      });
    } catch (error) {
      console.error('❌ Error obteniendo viajes disponibles:', error);
      return {
        success: false,
        error: error.message,
        trips: []
      };
    }
  }

  // Limpiar todos los listeners
  cleanup() {
    console.log('🧹 Limpiando listeners de Firebase...');
    this.listeners.forEach((unsubscribe) => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    });
    this.listeners.clear();
  }
}

// Exportar instancia singleton
export const firebaseService = new FirebaseService();
export default firebaseService;
