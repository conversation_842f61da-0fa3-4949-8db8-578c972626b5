import AsyncStorage from '@react-native-async-storage/async-storage';

class GlobalStorageService {
  constructor() {
    this.listeners = new Map();
    this.pollingInterval = null;
    this.isPolling = false;
  }

  // Crear un viaje global
  async createTrip(tripData) {
    try {
      console.log('🌐 Creando viaje en almacenamiento global:', tripData);
      
      const trip = {
        ...tripData,
        id: `trip-${Date.now()}`,
        status: 'pending',
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      
      // Guardar el viaje individual
      await AsyncStorage.setItem(`global_trip_${trip.id}`, JSON.stringify(trip));
      
      // Actualizar lista de viajes activos
      const activeTrips = await this.getActiveTrips();
      activeTrips.push(trip);
      await AsyncStorage.setItem('global_active_trips', JSON.stringify(activeTrips));
      
      console.log('✅ Viaje creado globalmente:', trip.id);
      
      return {
        success: true,
        trip: trip
      };
    } catch (error) {
      console.error('❌ Error creando viaje global:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Obtener viajes activos
  async getActiveTrips() {
    try {
      const activeTripsStr = await AsyncStorage.getItem('global_active_trips');
      return activeTripsStr ? JSON.parse(activeTripsStr) : [];
    } catch (error) {
      console.error('❌ Error obteniendo viajes activos:', error);
      return [];
    }
  }

  // Enviar solicitud de conductor
  async sendDriverRequest(tripId, driverInfo) {
    try {
      console.log('🚗 Enviando solicitud de conductor global:', { tripId, driverInfo });
      
      const request = {
        id: `request-${Date.now()}`,
        tripId: tripId,
        driverInfo: driverInfo,
        status: 'pending',
        timestamp: Date.now()
      };
      
      // Guardar la solicitud
      await AsyncStorage.setItem(`global_request_${request.id}`, JSON.stringify(request));
      
      // Actualizar lista de solicitudes para el viaje
      const requestsKey = `global_trip_requests_${tripId}`;
      const existingRequestsStr = await AsyncStorage.getItem(requestsKey);
      const existingRequests = existingRequestsStr ? JSON.parse(existingRequestsStr) : [];
      
      existingRequests.push(request);
      await AsyncStorage.setItem(requestsKey, JSON.stringify(existingRequests));
      
      console.log('✅ Solicitud de conductor enviada globalmente:', request.id);
      
      // Notificar a los listeners
      this.notifyListeners(`trip_requests_${tripId}`, existingRequests);
      
      return {
        success: true,
        request: request
      };
    } catch (error) {
      console.error('❌ Error enviando solicitud de conductor:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Escuchar solicitudes de conductores
  listenToDriverRequests(tripId, callback) {
    console.log('👂 Configurando listener para solicitudes del viaje:', tripId);
    
    const listenerKey = `trip_requests_${tripId}`;
    
    if (!this.listeners.has(listenerKey)) {
      this.listeners.set(listenerKey, []);
    }
    
    this.listeners.get(listenerKey).push(callback);
    
    // Iniciar polling si no está activo
    this.startPolling();
    
    // Cargar solicitudes existentes inmediatamente
    this.loadAndNotifyRequests(tripId);
    
    // Retornar función de cleanup
    return () => {
      const callbacks = this.listeners.get(listenerKey);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
        if (callbacks.length === 0) {
          this.listeners.delete(listenerKey);
        }
      }
      
      // Detener polling si no hay más listeners
      if (this.listeners.size === 0) {
        this.stopPolling();
      }
    };
  }

  // Cargar y notificar solicitudes
  async loadAndNotifyRequests(tripId) {
    try {
      const requestsKey = `global_trip_requests_${tripId}`;
      const requestsStr = await AsyncStorage.getItem(requestsKey);
      const requests = requestsStr ? JSON.parse(requestsStr) : [];
      
      console.log('📥 Solicitudes cargadas para viaje', tripId, ':', requests.length);
      
      this.notifyListeners(`trip_requests_${tripId}`, requests);
    } catch (error) {
      console.error('❌ Error cargando solicitudes:', error);
    }
  }

  // Iniciar polling para actualizaciones
  startPolling() {
    if (this.isPolling) return;
    
    console.log('🔄 Iniciando polling para actualizaciones globales...');
    this.isPolling = true;
    
    this.pollingInterval = setInterval(() => {
      this.checkForUpdates();
    }, 2000); // Verificar cada 2 segundos
  }

  // Detener polling
  stopPolling() {
    if (this.pollingInterval) {
      console.log('⏹️ Deteniendo polling global');
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      this.isPolling = false;
    }
  }

  // Verificar actualizaciones
  async checkForUpdates() {
    for (const [listenerKey, callbacks] of this.listeners.entries()) {
      if (listenerKey.startsWith('trip_requests_')) {
        const tripId = listenerKey.replace('trip_requests_', '');
        await this.loadAndNotifyRequests(tripId);
      }
    }
  }

  // Notificar a los listeners
  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error en listener:', error);
        }
      });
    }
  }

  // Responder a solicitud de conductor
  async respondToDriverRequest(tripId, requestId, accepted, additionalData = {}) {
    try {
      console.log('📤 Enviando respuesta del pasajero:', { tripId, requestId, accepted });
      
      const response = {
        tripId: tripId,
        requestId: requestId,
        accepted: accepted,
        timestamp: Date.now(),
        ...additionalData
      };
      
      // Guardar la respuesta
      const responseKey = `global_response_${tripId}_${requestId}`;
      await AsyncStorage.setItem(responseKey, JSON.stringify(response));
      
      // Si fue aceptado, limpiar otras solicitudes
      if (accepted) {
        const requestsKey = `global_trip_requests_${tripId}`;
        await AsyncStorage.removeItem(requestsKey);
      }
      
      console.log('✅ Respuesta del pasajero guardada globalmente');
      
      return {
        success: true,
        response: response
      };
    } catch (error) {
      console.error('❌ Error enviando respuesta del pasajero:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Verificar respuesta del pasajero (para conductores)
  async checkPassengerResponse(tripId, requestId) {
    try {
      const responseKey = `global_response_${tripId}_${requestId}`;
      const responseStr = await AsyncStorage.getItem(responseKey);
      
      if (responseStr) {
        const response = JSON.parse(responseStr);
        console.log('📥 Respuesta del pasajero encontrada:', response);
        
        // Limpiar la respuesta para no procesarla de nuevo
        await AsyncStorage.removeItem(responseKey);
        
        return response;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error verificando respuesta del pasajero:', error);
      return null;
    }
  }

  // Limpiar todos los datos
  async cleanup() {
    console.log('🧹 Limpiando almacenamiento global...');
    this.stopPolling();
    this.listeners.clear();
    
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const globalKeys = allKeys.filter(key => key.startsWith('global_'));
      await AsyncStorage.multiRemove(globalKeys);
      console.log('✅ Almacenamiento global limpiado');
    } catch (error) {
      console.error('❌ Error limpiando almacenamiento:', error);
    }
  }
}

// Exportar instancia singleton
export const globalStorageService = new GlobalStorageService();
export default globalStorageService;
