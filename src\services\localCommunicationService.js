import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';

class LocalCommunicationService {
  constructor() {
    this.listeners = new Map();
    this.pollingInterval = null;
    this.isPolling = false;
    this.lastChecked = {};
    
    // Usar directorio de documentos para archivos compartidos
    this.sharedDir = FileSystem.documentDirectory + 'maclaren_shared/';
    this.initializeSharedDirectory();
  }

  // Inicializar directorio compartido
  async initializeSharedDirectory() {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.sharedDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.sharedDir, { intermediates: true });
        console.log('📁 Directorio compartido creado:', this.sharedDir);
      }
    } catch (error) {
      console.error('❌ Error creando directorio compartido:', error);
    }
  }

  // Crear viaje compartido
  async createTrip(tripData) {
    try {
      console.log('🚗 Creando viaje compartido:', tripData);
      
      const trip = {
        ...tripData,
        id: `trip-${Date.now()}`,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Guardar en archivo compartido
      const tripFile = this.sharedDir + `trip_${trip.id}.json`;
      await FileSystem.writeAsStringAsync(tripFile, JSON.stringify(trip));
      
      // Actualizar índice de viajes activos
      await this.updateActiveTripsIndex(trip, 'add');
      
      console.log('✅ Viaje compartido creado:', trip.id);
      
      return {
        success: true,
        trip: trip
      };
    } catch (error) {
      console.error('❌ Error creando viaje compartido:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Actualizar índice de viajes activos
  async updateActiveTripsIndex(trip, action) {
    try {
      const indexFile = this.sharedDir + 'active_trips_index.json';
      let activeTrips = [];
      
      // Leer índice existente
      const indexInfo = await FileSystem.getInfoAsync(indexFile);
      if (indexInfo.exists) {
        const indexContent = await FileSystem.readAsStringAsync(indexFile);
        activeTrips = JSON.parse(indexContent);
      }
      
      if (action === 'add') {
        // Agregar viaje si no existe
        const exists = activeTrips.find(t => t.id === trip.id);
        if (!exists) {
          activeTrips.push({
            id: trip.id,
            status: trip.status,
            createdAt: trip.createdAt,
            updatedAt: trip.updatedAt
          });
        }
      } else if (action === 'remove') {
        // Remover viaje
        activeTrips = activeTrips.filter(t => t.id !== trip.id);
      }
      
      // Guardar índice actualizado
      await FileSystem.writeAsStringAsync(indexFile, JSON.stringify(activeTrips));
      
    } catch (error) {
      console.error('❌ Error actualizando índice de viajes:', error);
    }
  }

  // Obtener viajes activos
  async getActiveTrips() {
    try {
      const indexFile = this.sharedDir + 'active_trips_index.json';
      const indexInfo = await FileSystem.getInfoAsync(indexFile);
      
      if (!indexInfo.exists) {
        return { success: true, trips: [] };
      }
      
      const indexContent = await FileSystem.readAsStringAsync(indexFile);
      const tripIds = JSON.parse(indexContent);
      
      const trips = [];
      
      // Cargar cada viaje
      for (const tripInfo of tripIds) {
        try {
          const tripFile = this.sharedDir + `trip_${tripInfo.id}.json`;
          const tripFileInfo = await FileSystem.getInfoAsync(tripFile);
          
          if (tripFileInfo.exists) {
            const tripContent = await FileSystem.readAsStringAsync(tripFile);
            const trip = JSON.parse(tripContent);
            
            if (trip.status === 'pending') {
              trips.push(trip);
            }
          }
        } catch (error) {
          console.warn('⚠️ Error cargando viaje:', tripInfo.id, error);
        }
      }
      
      console.log('📋 Viajes activos cargados:', trips.length);
      
      return {
        success: true,
        trips: trips
      };
    } catch (error) {
      console.error('❌ Error obteniendo viajes activos:', error);
      return {
        success: true,
        trips: []
      };
    }
  }

  // Enviar solicitud de conductor
  async sendDriverRequest(tripId, driverInfo) {
    try {
      console.log('🚗 Enviando solicitud de conductor compartida:', { tripId, driverInfo });
      
      const request = {
        id: `request-${Date.now()}`,
        tripId: tripId,
        driverInfo: driverInfo,
        status: 'pending',
        timestamp: new Date().toISOString()
      };
      
      // Guardar solicitud en archivo compartido
      const requestFile = this.sharedDir + `request_${request.id}.json`;
      await FileSystem.writeAsStringAsync(requestFile, JSON.stringify(request));
      
      // Actualizar índice de solicitudes para el viaje
      await this.updateRequestsIndex(tripId, request, 'add');
      
      console.log('✅ Solicitud de conductor compartida enviada:', request.id);
      
      return true;
    } catch (error) {
      console.error('❌ Error enviando solicitud compartida:', error);
      return false;
    }
  }

  // Actualizar índice de solicitudes
  async updateRequestsIndex(tripId, request, action) {
    try {
      const indexFile = this.sharedDir + `requests_${tripId}_index.json`;
      let requests = [];
      
      // Leer índice existente
      const indexInfo = await FileSystem.getInfoAsync(indexFile);
      if (indexInfo.exists) {
        const indexContent = await FileSystem.readAsStringAsync(indexFile);
        requests = JSON.parse(indexContent);
      }
      
      if (action === 'add') {
        // Agregar solicitud si no existe
        const exists = requests.find(r => r.id === request.id);
        if (!exists) {
          requests.push({
            id: request.id,
            status: request.status,
            timestamp: request.timestamp
          });
        }
      } else if (action === 'remove') {
        // Remover solicitud
        requests = requests.filter(r => r.id !== request.id);
      }
      
      // Guardar índice actualizado
      await FileSystem.writeAsStringAsync(indexFile, JSON.stringify(requests));
      
    } catch (error) {
      console.error('❌ Error actualizando índice de solicitudes:', error);
    }
  }

  // Escuchar solicitudes de conductores
  listenToDriverRequests(tripId, callback) {
    console.log('👂 Configurando listener para solicitudes del viaje:', tripId);
    
    const listenerKey = `trip_requests_${tripId}`;
    
    if (!this.listeners.has(listenerKey)) {
      this.listeners.set(listenerKey, []);
    }
    
    this.listeners.get(listenerKey).push(callback);
    
    // Iniciar polling si no está activo
    this.startPolling();
    
    // Cargar solicitudes existentes inmediatamente
    this.loadAndNotifyRequests(tripId);
    
    // Retornar función de cleanup
    return () => {
      const callbacks = this.listeners.get(listenerKey);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
        if (callbacks.length === 0) {
          this.listeners.delete(listenerKey);
        }
      }
      
      // Detener polling si no hay más listeners
      if (this.listeners.size === 0) {
        this.stopPolling();
      }
    };
  }

  // Cargar y notificar solicitudes
  async loadAndNotifyRequests(tripId) {
    try {
      const indexFile = this.sharedDir + `requests_${tripId}_index.json`;
      const indexInfo = await FileSystem.getInfoAsync(indexFile);
      
      if (!indexInfo.exists) {
        this.notifyListeners(`trip_requests_${tripId}`, []);
        return;
      }
      
      const indexContent = await FileSystem.readAsStringAsync(indexFile);
      const requestIds = JSON.parse(indexContent);
      
      const requests = [];
      
      // Cargar cada solicitud
      for (const requestInfo of requestIds) {
        try {
          const requestFile = this.sharedDir + `request_${requestInfo.id}.json`;
          const requestFileInfo = await FileSystem.getInfoAsync(requestFile);
          
          if (requestFileInfo.exists) {
            const requestContent = await FileSystem.readAsStringAsync(requestFile);
            const request = JSON.parse(requestContent);
            
            if (request.status === 'pending') {
              requests.push(request);
            }
          }
        } catch (error) {
          console.warn('⚠️ Error cargando solicitud:', requestInfo.id, error);
        }
      }
      
      console.log('📥 Solicitudes cargadas para viaje', tripId, ':', requests.length);
      
      this.notifyListeners(`trip_requests_${tripId}`, requests);
    } catch (error) {
      console.error('❌ Error cargando solicitudes:', error);
    }
  }

  // Iniciar polling para actualizaciones
  startPolling() {
    if (this.isPolling) return;
    
    console.log('🔄 Iniciando polling para comunicación local...');
    this.isPolling = true;
    
    this.pollingInterval = setInterval(() => {
      this.checkForUpdates();
    }, 2000); // Verificar cada 2 segundos
  }

  // Detener polling
  stopPolling() {
    if (this.pollingInterval) {
      console.log('⏹️ Deteniendo polling local');
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      this.isPolling = false;
    }
  }

  // Verificar actualizaciones
  async checkForUpdates() {
    for (const [listenerKey, callbacks] of this.listeners.entries()) {
      if (listenerKey.startsWith('trip_requests_')) {
        const tripId = listenerKey.replace('trip_requests_', '');
        await this.loadAndNotifyRequests(tripId);
      }
    }
  }

  // Notificar a los listeners
  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error en listener:', error);
        }
      });
    }
  }

  // Responder a solicitud de conductor
  async respondToDriverRequest(tripId, requestId, accepted, additionalData = {}) {
    try {
      console.log('📤 Enviando respuesta del pasajero compartida:', { tripId, requestId, accepted });
      
      const response = {
        tripId: tripId,
        requestId: requestId,
        accepted: accepted,
        timestamp: new Date().toISOString(),
        ...additionalData
      };
      
      // Guardar la respuesta
      const responseFile = this.sharedDir + `response_${tripId}_${requestId}.json`;
      await FileSystem.writeAsStringAsync(responseFile, JSON.stringify(response));
      
      // Si fue aceptado, limpiar solicitudes
      if (accepted) {
        await this.clearTripRequests(tripId);
      }
      
      console.log('✅ Respuesta del pasajero compartida guardada');
      
      return true;
    } catch (error) {
      console.error('❌ Error enviando respuesta compartida:', error);
      return false;
    }
  }

  // Limpiar solicitudes de un viaje
  async clearTripRequests(tripId) {
    try {
      const indexFile = this.sharedDir + `requests_${tripId}_index.json`;
      const indexInfo = await FileSystem.getInfoAsync(indexFile);
      
      if (indexInfo.exists) {
        await FileSystem.deleteAsync(indexFile);
      }
      
      console.log('🧹 Solicitudes del viaje limpiadas:', tripId);
    } catch (error) {
      console.error('❌ Error limpiando solicitudes:', error);
    }
  }

  // Verificar respuesta del pasajero (para conductores)
  async checkPassengerResponse(tripId, requestId) {
    try {
      const responseFile = this.sharedDir + `response_${tripId}_${requestId}.json`;
      const responseInfo = await FileSystem.getInfoAsync(responseFile);
      
      if (responseInfo.exists) {
        const responseContent = await FileSystem.readAsStringAsync(responseFile);
        const response = JSON.parse(responseContent);
        
        console.log('📥 Respuesta del pasajero encontrada:', response);
        
        // Limpiar la respuesta para no procesarla de nuevo
        await FileSystem.deleteAsync(responseFile);
        
        return response;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error verificando respuesta del pasajero:', error);
      return null;
    }
  }

  // Conectar (simulado)
  async connect(userData) {
    console.log('🔗 Activando comunicación local compartida');
    console.log('👤 Datos de usuario:', userData);
    
    await this.initializeSharedDirectory();
    
    return true;
  }

  // Verificar si está "conectado"
  isSocketConnected() {
    return true;
  }

  // Agregar listener
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remover listener
  removeListener(event, callback) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Desconectar
  disconnect() {
    console.log('👋 Desconectando comunicación local...');
    this.stopPolling();
    this.listeners.clear();
  }

  // Limpiar todos los datos
  async cleanup() {
    console.log('🧹 Limpiando datos de comunicación local...');
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.sharedDir);
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(this.sharedDir, { idempotent: true });
        await this.initializeSharedDirectory();
      }
      console.log('✅ Datos de comunicación local limpiados');
    } catch (error) {
      console.error('❌ Error limpiando datos:', error);
    }
  }
}

// Exportar instancia singleton
export const localCommunicationService = new LocalCommunicationService();
export default localCommunicationService;
