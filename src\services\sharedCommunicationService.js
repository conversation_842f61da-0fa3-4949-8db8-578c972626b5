import AsyncStorage from '@react-native-async-storage/async-storage';

class SharedCommunicationService {
  constructor() {
    this.listeners = new Map();
    this.pollingInterval = null;
    this.isPolling = false;
    this.lastChecked = {};
  }

  // Crear viaje compartido
  async createTrip(tripData) {
    try {
      console.log('🚗 Creando viaje compartido:', tripData);
      
      const trip = {
        ...tripData,
        id: `trip-${Date.now()}`,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Guardar viaje individual
      await AsyncStorage.setItem(`shared_trip_${trip.id}`, JSON.stringify(trip));
      
      // Actualizar lista de viajes activos
      const activeTrips = await this.getActiveTrips();
      const updatedTrips = [...activeTrips.trips, trip];
      await AsyncStorage.setItem('shared_active_trips', JSON.stringify(updatedTrips));
      
      // Crear timestamp de actualización para notificar a conductores
      await AsyncStorage.setItem('shared_trips_updated', Date.now().toString());
      
      console.log('✅ Viaje compartido creado:', trip.id);
      
      return {
        success: true,
        trip: trip
      };
    } catch (error) {
      console.error('❌ Error creando viaje compartido:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Obtener viajes activos
  async getActiveTrips() {
    try {
      const activeTripsStr = await AsyncStorage.getItem('shared_active_trips');
      const trips = activeTripsStr ? JSON.parse(activeTripsStr) : [];
      
      // Filtrar solo viajes pendientes
      const pendingTrips = trips.filter(trip => trip.status === 'pending');
      
      console.log('📋 Viajes activos compartidos:', pendingTrips.length);
      
      return {
        success: true,
        trips: pendingTrips
      };
    } catch (error) {
      console.error('❌ Error obteniendo viajes activos:', error);
      return {
        success: true,
        trips: []
      };
    }
  }

  // Enviar solicitud de conductor
  async sendDriverRequest(tripId, driverInfo) {
    try {
      console.log('🚗 Enviando solicitud de conductor compartida:', { tripId, driverInfo });
      
      const request = {
        id: `request-${Date.now()}`,
        tripId: tripId,
        driverInfo: driverInfo,
        status: 'pending',
        timestamp: new Date().toISOString()
      };
      
      // Guardar solicitud individual
      await AsyncStorage.setItem(`shared_request_${request.id}`, JSON.stringify(request));
      
      // Actualizar lista de solicitudes para el viaje
      const requestsKey = `shared_requests_${tripId}`;
      const existingRequestsStr = await AsyncStorage.getItem(requestsKey);
      const existingRequests = existingRequestsStr ? JSON.parse(existingRequestsStr) : [];
      
      const updatedRequests = [...existingRequests, request];
      await AsyncStorage.setItem(requestsKey, JSON.stringify(updatedRequests));
      
      // Crear timestamp de actualización para notificar al pasajero
      await AsyncStorage.setItem(`shared_requests_${tripId}_updated`, Date.now().toString());
      
      console.log('✅ Solicitud de conductor compartida enviada:', request.id);
      
      return true;
    } catch (error) {
      console.error('❌ Error enviando solicitud compartida:', error);
      return false;
    }
  }

  // Escuchar solicitudes de conductores
  listenToDriverRequests(tripId, callback) {
    console.log('👂 Configurando listener para solicitudes del viaje:', tripId);
    
    const listenerKey = `trip_requests_${tripId}`;
    
    if (!this.listeners.has(listenerKey)) {
      this.listeners.set(listenerKey, []);
    }
    
    this.listeners.get(listenerKey).push(callback);
    
    // Inicializar timestamp de última verificación
    this.lastChecked[`requests_${tripId}`] = 0;
    
    // Iniciar polling si no está activo
    this.startPolling();
    
    // Cargar solicitudes existentes inmediatamente
    this.loadAndNotifyRequests(tripId);
    
    // Retornar función de cleanup
    return () => {
      const callbacks = this.listeners.get(listenerKey);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
        if (callbacks.length === 0) {
          this.listeners.delete(listenerKey);
        }
      }
      
      // Limpiar timestamp
      delete this.lastChecked[`requests_${tripId}`];
      
      // Detener polling si no hay más listeners
      if (this.listeners.size === 0) {
        this.stopPolling();
      }
    };
  }

  // Cargar y notificar solicitudes
  async loadAndNotifyRequests(tripId) {
    try {
      const requestsKey = `shared_requests_${tripId}`;
      const requestsStr = await AsyncStorage.getItem(requestsKey);
      const requests = requestsStr ? JSON.parse(requestsStr) : [];
      
      // Filtrar solo solicitudes pendientes
      const pendingRequests = requests.filter(req => req.status === 'pending');
      
      console.log('📥 Solicitudes cargadas para viaje', tripId, ':', pendingRequests.length);
      
      this.notifyListeners(`trip_requests_${tripId}`, pendingRequests);
    } catch (error) {
      console.error('❌ Error cargando solicitudes:', error);
    }
  }

  // Iniciar polling para actualizaciones
  startPolling() {
    if (this.isPolling) return;
    
    console.log('🔄 Iniciando polling para comunicación compartida...');
    this.isPolling = true;
    
    this.pollingInterval = setInterval(() => {
      this.checkForUpdates();
    }, 1500); // Verificar cada 1.5 segundos para mayor responsividad
  }

  // Detener polling
  stopPolling() {
    if (this.pollingInterval) {
      console.log('⏹️ Deteniendo polling compartido');
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      this.isPolling = false;
    }
  }

  // Verificar actualizaciones
  async checkForUpdates() {
    try {
      // Verificar actualizaciones de solicitudes para cada listener
      for (const [listenerKey, callbacks] of this.listeners.entries()) {
        if (listenerKey.startsWith('trip_requests_')) {
          const tripId = listenerKey.replace('trip_requests_', '');
          
          // Verificar si hay actualizaciones
          const updatedTimestampStr = await AsyncStorage.getItem(`shared_requests_${tripId}_updated`);
          const updatedTimestamp = updatedTimestampStr ? parseInt(updatedTimestampStr) : 0;
          const lastChecked = this.lastChecked[`requests_${tripId}`] || 0;
          
          if (updatedTimestamp > lastChecked) {
            console.log('🔔 Nueva actualización detectada para viaje:', tripId);
            this.lastChecked[`requests_${tripId}`] = updatedTimestamp;
            await this.loadAndNotifyRequests(tripId);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error verificando actualizaciones:', error);
    }
  }

  // Notificar a los listeners
  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error en listener:', error);
        }
      });
    }
  }

  // Responder a solicitud de conductor
  async respondToDriverRequest(tripId, requestId, accepted, additionalData = {}) {
    try {
      console.log('📤 Enviando respuesta del pasajero compartida:', { tripId, requestId, accepted });
      
      const response = {
        tripId: tripId,
        requestId: requestId,
        accepted: accepted,
        timestamp: new Date().toISOString(),
        ...additionalData
      };
      
      // Guardar la respuesta
      const responseKey = `shared_response_${tripId}_${requestId}`;
      await AsyncStorage.setItem(responseKey, JSON.stringify(response));
      
      // Crear timestamp de actualización para notificar al conductor
      await AsyncStorage.setItem(`shared_response_${tripId}_${requestId}_updated`, Date.now().toString());
      
      // Si fue aceptado, limpiar solicitudes del viaje
      if (accepted) {
        await this.clearTripRequests(tripId);
      }
      
      console.log('✅ Respuesta del pasajero compartida guardada');
      
      return true;
    } catch (error) {
      console.error('❌ Error enviando respuesta compartida:', error);
      return false;
    }
  }

  // Limpiar solicitudes de un viaje
  async clearTripRequests(tripId) {
    try {
      const requestsKey = `shared_requests_${tripId}`;
      await AsyncStorage.removeItem(requestsKey);
      await AsyncStorage.removeItem(`shared_requests_${tripId}_updated`);
      
      console.log('🧹 Solicitudes del viaje limpiadas:', tripId);
    } catch (error) {
      console.error('❌ Error limpiando solicitudes:', error);
    }
  }

  // Verificar respuesta del pasajero (para conductores)
  async checkPassengerResponse(tripId, requestId) {
    try {
      const responseKey = `shared_response_${tripId}_${requestId}`;
      const responseStr = await AsyncStorage.getItem(responseKey);
      
      if (responseStr) {
        const response = JSON.parse(responseStr);
        console.log('📥 Respuesta del pasajero encontrada:', response);
        
        // Limpiar la respuesta para no procesarla de nuevo
        await AsyncStorage.removeItem(responseKey);
        await AsyncStorage.removeItem(`shared_response_${tripId}_${requestId}_updated`);
        
        return response;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error verificando respuesta del pasajero:', error);
      return null;
    }
  }

  // Conectar (simulado)
  async connect(userData) {
    console.log('🔗 Activando comunicación compartida');
    console.log('👤 Datos de usuario:', userData);
    
    return true;
  }

  // Verificar si está "conectado"
  isSocketConnected() {
    return true;
  }

  // Agregar listener
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remover listener
  removeListener(event, callback) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Desconectar
  disconnect() {
    console.log('👋 Desconectando comunicación compartida...');
    this.stopPolling();
    this.listeners.clear();
    this.lastChecked = {};
  }

  // Limpiar todos los datos
  async cleanup() {
    console.log('🧹 Limpiando datos de comunicación compartida...');
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const sharedKeys = allKeys.filter(key => key.startsWith('shared_'));
      await AsyncStorage.multiRemove(sharedKeys);
      console.log('✅ Datos de comunicación compartida limpiados');
    } catch (error) {
      console.error('❌ Error limpiando datos:', error);
    }
  }
}

// Exportar instancia singleton
export const sharedCommunicationService = new SharedCommunicationService();
export default sharedCommunicationService;
