import io from 'socket.io-client';

class SimpleServerService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
    this.serverUrl = 'http://localhost:3001';
  }

  // Conectar al servidor
  async connect(userData) {
    try {
      console.log('🔌 Conectando al servidor simple...', this.serverUrl);
      
      this.socket = io(this.serverUrl, {
        transports: ['polling', 'websocket'],
        timeout: 10000,
        forceNew: true,
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 2000,
      });

      this.socket.on('connect', () => {
        console.log('✅ Conectado al servidor simple:', this.socket.id);
        this.isConnected = true;
        
        // Registrar usuario
        this.socket.emit('register-user', userData);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('❌ Desconectado del servidor simple:', reason);
        this.isConnected = false;
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ Error de conexión al servidor simple:', error.message);
        this.isConnected = false;
      });

      // Configurar listeners para eventos
      this.setupEventListeners();

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve(false);
        }, 10000);

        this.socket.on('connect', () => {
          clearTimeout(timeout);
          resolve(true);
        });
      });
    } catch (error) {
      console.error('❌ Error conectando al servidor simple:', error);
      return false;
    }
  }

  // Configurar listeners de eventos
  setupEventListeners() {
    // Nuevo viaje disponible (para conductores)
    this.socket.on('new-trip', (trip) => {
      console.log('🚗 Nuevo viaje disponible:', trip);
      this.notifyListeners('new-trip', trip);
    });

    // Solicitud de conductor recibida (para pasajeros)
    this.socket.on('driver-request-received', (request) => {
      console.log('📥 Solicitud de conductor recibida:', request);
      this.notifyListeners('driver-request-received', request);
    });

    // Respuesta del pasajero (para conductores)
    this.socket.on('passenger-response', (response) => {
      console.log('📥 Respuesta del pasajero recibida:', response);
      this.notifyListeners('passenger-response', response);
    });
  }

  // Crear viaje
  async createTrip(tripData) {
    try {
      console.log('🚗 Creando viaje en servidor simple:', tripData);
      
      const response = await fetch(`${this.serverUrl}/api/trips`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tripData),
      });

      const result = await response.json();
      
      if (result.success) {
        // Unirse a la sala del viaje
        if (this.socket && this.isConnected) {
          this.socket.emit('join-trip', result.trip.id);
        }
        
        console.log('✅ Viaje creado en servidor simple:', result.trip.id);
        return result;
      } else {
        throw new Error('Error creando viaje');
      }
    } catch (error) {
      console.error('❌ Error creando viaje:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Obtener viajes disponibles
  async getAvailableTrips() {
    try {
      console.log('📍 Obteniendo viajes disponibles del servidor simple...');
      
      const response = await fetch(`${this.serverUrl}/api/trips`);
      const result = await response.json();
      
      console.log('📋 Viajes disponibles obtenidos:', result.trips?.length || 0);
      return result;
    } catch (error) {
      console.error('❌ Error obteniendo viajes:', error);
      return {
        success: false,
        error: error.message,
        trips: []
      };
    }
  }

  // Enviar solicitud de conductor
  sendDriverRequest(tripId, driverInfo) {
    if (!this.socket || !this.isConnected) {
      console.error('❌ No conectado al servidor');
      return false;
    }

    console.log('📤 Enviando solicitud de conductor:', { tripId, driverInfo });
    
    this.socket.emit('driver-request', {
      tripId: tripId,
      driverInfo: driverInfo,
      timestamp: new Date().toISOString()
    });

    return true;
  }

  // Responder a solicitud de conductor
  respondToDriverRequest(tripId, driverId, accepted, additionalData = {}) {
    if (!this.socket || !this.isConnected) {
      console.error('❌ No conectado al servidor');
      return false;
    }

    console.log('📤 Enviando respuesta del pasajero:', { tripId, driverId, accepted });
    
    this.socket.emit('passenger-response', {
      tripId: tripId,
      driverId: driverId,
      accepted: accepted,
      ...additionalData,
      timestamp: new Date().toISOString()
    });

    return true;
  }

  // Agregar listener
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remover listener
  removeListener(event, callback) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Notificar listeners
  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error en listener:', error);
        }
      });
    }
  }

  // Verificar si está conectado
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }

  // Desconectar
  disconnect() {
    if (this.socket) {
      console.log('👋 Desconectando del servidor simple...');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.listeners.clear();
    }
  }
}

// Exportar instancia singleton
export const simpleServerService = new SimpleServerService();
export default simpleServerService;
