import io from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
  }

  async connect() {
    try {
      // URL del servidor (ajustar según tu configuración)
      const serverUrl = 'http://********:3000'; // Para emulador Android
      // const serverUrl = 'http://localhost:3000'; // Para web/iOS

      console.log('🔌 Conectando a Socket.IO...', serverUrl);

      this.socket = io(serverUrl, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        autoConnect: true,
      });

      this.socket.on('connect', () => {
        console.log('✅ Socket.IO conectado:', this.socket.id);
        this.isConnected = true;
        this.setupUserSession();
      });

      this.socket.on('disconnect', () => {
        console.log('❌ Socket.IO desconectado');
        this.isConnected = false;
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ Error de conexión Socket.IO:', error);
        this.isConnected = false;
      });

      // Configurar listeners de eventos de viajes
      this.setupTripListeners();

    } catch (error) {
      console.error('❌ Error al conectar Socket.IO:', error);
    }
  }

  async setupUserSession() {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        this.socket.emit('user-session', {
          userId: user.id,
          userType: user.userType || 'passenger'
        });
        console.log('👤 Sesión de usuario configurada:', user.id);
      }
    } catch (error) {
      console.error('❌ Error configurando sesión:', error);
    }
  }

  setupTripListeners() {
    if (!this.socket) return;

    // Escuchar cuando un conductor acepta un viaje
    this.socket.on('driver-request-received', (data) => {
      console.log('🚗 Solicitud de conductor recibida:', data);
      this.notifyListeners('driver-request-received', data);
    });

    // Escuchar cuando el pasajero acepta/rechaza un conductor
    this.socket.on('passenger-response', (data) => {
      console.log('👤 Respuesta del pasajero:', data);
      this.notifyListeners('passenger-response', data);
    });

    // Escuchar actualizaciones de estado del viaje
    this.socket.on('trip-status-updated', (data) => {
      console.log('📱 Estado del viaje actualizado:', data);
      this.notifyListeners('trip-status-updated', data);
    });

    // Escuchar ubicación del conductor
    this.socket.on('driver-location-updated', (data) => {
      console.log('📍 Ubicación del conductor actualizada:', data);
      this.notifyListeners('driver-location-updated', data);
    });
  }

  // Unirse a la sala de un viaje específico
  joinTripRoom(tripId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-trip', tripId);
      console.log('🏠 Unido a la sala del viaje:', tripId);
    }
  }

  // Salir de la sala de un viaje
  leaveTripRoom(tripId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave-trip', tripId);
      console.log('🚪 Saliendo de la sala del viaje:', tripId);
    }
  }

  // Notificar que un conductor quiere aceptar un viaje
  notifyDriverRequest(tripId, driverInfo) {
    if (this.socket && this.isConnected) {
      this.socket.emit('driver-request', {
        tripId,
        driverInfo,
        timestamp: new Date().toISOString()
      });
      console.log('📤 Solicitud de conductor enviada:', tripId);
    }
  }

  // Notificar respuesta del pasajero
  notifyPassengerResponse(tripId, driverId, accepted) {
    if (this.socket && this.isConnected) {
      this.socket.emit('passenger-response', {
        tripId,
        driverId,
        accepted,
        timestamp: new Date().toISOString()
      });
      console.log('📤 Respuesta del pasajero enviada:', { tripId, driverId, accepted });
    }
  }

  // Actualizar ubicación del conductor
  updateDriverLocation(tripId, location) {
    if (this.socket && this.isConnected) {
      this.socket.emit('update-driver-location', {
        tripId,
        location,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Notificar cambio de estado del viaje
  notifyTripStatusChange(tripId, status, data = {}) {
    if (this.socket && this.isConnected) {
      this.socket.emit('trip-status-change', {
        tripId,
        status,
        data,
        timestamp: new Date().toISOString()
      });
      console.log('📤 Cambio de estado enviado:', { tripId, status });
    }
  }

  // Agregar listener para eventos
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remover listener
  removeListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Notificar a todos los listeners de un evento
  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error en listener:', error);
        }
      });
    }
  }

  // Desconectar
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.listeners.clear();
      console.log('🔌 Socket.IO desconectado manualmente');
    }
  }

  // Verificar si está conectado
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }
}

// Exportar instancia singleton
export const socketService = new SocketService();
export default socketService;
