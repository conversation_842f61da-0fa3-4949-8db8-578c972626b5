import { createApiInstance, apiRequest, OFFLINE_DATA, OfflineStorage } from '../config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';

const api = createApiInstance();

export const tripService = {
  async createTripRequest(tripData) {
    // Obtener información del usuario actual
    const userToken = await AsyncStorage.getItem('userToken');
    const userDataStr = await AsyncStorage.getItem('userData');
    const userData = userDataStr ? JSON.parse(userDataStr) : OFFLINE_DATA.passengerUser;

    const newTrip = {
      _id: 'offline-trip-' + Date.now(),
      pickupLocation: tripData.pickupLocation,
      destination: tripData.destination,
      offeredPrice: tripData.offeredPrice,
      passengerNotes: tripData.notes,
      status: 'pending',
      passenger: {
        id: userData.id,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
      },
      createdAt: new Date().toISOString(),
    };

    const result = await apiRequest(
      () => api.post('/trips/request', {
        pickupLocation: tripData.pickupLocation,
        destination: tripData.destination,
        offeredPrice: tripData.offeredPrice,
        passengerNotes: tripData.notes,
      }),
      {
        trip: newTrip,
      },
      'Error al crear solicitud de viaje'
    );

    if (result.success) {
      // Si estamos en modo offline, guardar el viaje localmente
      if (result.isOffline) {
        await OfflineStorage.saveCreatedTrip(result.data.trip);
        console.log('🚗 Viaje creado y guardado localmente');
      }

      return {
        success: true,
        trip: result.data.trip,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async getAvailableTrips(driverLocation) {
    console.log('🔍 Buscando viajes disponibles para ubicación:', driverLocation);

    const result = await apiRequest(
      () => api.get('/trips/available', {
        params: {
          latitude: driverLocation.latitude,
          longitude: driverLocation.longitude,
          radius: 10, // 10 km radius
        },
      }),
      null, // No usar datos estáticos, usar el sistema de almacenamiento
      'Error al obtener viajes disponibles'
    );

    if (result.success && !result.isOffline) {
      console.log('🌐 Viajes obtenidos desde servidor:', result.data.trips.length);
      return {
        success: true,
        trips: result.data.trips,
        isOffline: result.isOffline,
      };
    } else {
      // En caso de error o modo offline, usar el sistema de almacenamiento local
      console.log('📱 Obteniendo viajes desde almacenamiento local');
      const availableTrips = await OfflineStorage.getAvailableTrips();
      console.log('💾 Viajes locales encontrados:', availableTrips.length);

      return {
        success: true,
        trips: availableTrips,
        isOffline: true,
      };
    }
  },

  async acceptTrip(tripId) {
    // Obtener información del conductor actual
    const userDataStr = await AsyncStorage.getItem('userData');
    const userData = userDataStr ? JSON.parse(userDataStr) : OFFLINE_DATA.driverUser;

    const result = await apiRequest(
      () => api.post(`/trips/${tripId}/accept`, {
        driverId: userData.id,
        driverInfo: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          phone: userData.phone,
          vehicle: userData.vehicle,
          rating: userData.rating || 4.5,
        }
      }),
      {
        trip: {
          _id: tripId,
          status: 'driver_accepted',
          driver: {
            id: userData.id,
            firstName: userData.firstName,
            lastName: userData.lastName,
            phone: userData.phone,
            vehicle: userData.vehicle,
            rating: userData.rating || 4.5,
          },
          acceptedAt: new Date().toISOString(),
        }
      },
      'Error al aceptar viaje'
    );

    if (result.success) {
      return {
        success: true,
        trip: result.data.trip,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async passengerAcceptDriver(tripId, driverId) {
    const result = await apiRequest(
      () => api.post(`/trips/${tripId}/passenger-accept`, {
        driverId: driverId,
      }),
      {
        trip: {
          _id: tripId,
          status: 'confirmed',
          confirmedAt: new Date().toISOString(),
        }
      },
      'Error al confirmar conductor'
    );

    if (result.success) {
      return {
        success: true,
        trip: result.data.trip,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async rejectDriver(tripId, driverId) {
    const result = await apiRequest(
      () => api.post(`/trips/${tripId}/reject-driver`, {
        driverId: driverId,
      }),
      {
        message: 'Conductor rechazado'
      },
      'Error al rechazar conductor'
    );

    return result;
  },

  async openWazeNavigation(destination, label = 'Destino') {
    try {
      const { Linking } = require('react-native');

      // URL para abrir Waze con navegación
      const wazeUrl = `waze://?ll=${destination.latitude},${destination.longitude}&navigate=yes&z=10`;

      // URL alternativa para Google Maps si Waze no está disponible
      const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${destination.latitude},${destination.longitude}&travelmode=driving`;

      console.log('🗺️ Intentando abrir Waze para navegación...');

      // Verificar si Waze está instalado
      const canOpenWaze = await Linking.canOpenURL(wazeUrl);

      if (canOpenWaze) {
        console.log('✅ Abriendo Waze...');
        await Linking.openURL(wazeUrl);
        return { success: true, app: 'waze' };
      } else {
        console.log('⚠️ Waze no disponible, abriendo Google Maps...');
        await Linking.openURL(googleMapsUrl);
        return { success: true, app: 'google_maps' };
      }
    } catch (error) {
      console.error('❌ Error al abrir navegación:', error);
      return {
        success: false,
        error: 'No se pudo abrir la aplicación de navegación'
      };
    }
  },

  async startTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/start`);

      return {
        success: true,
        trip: response.data.trip,
      };
    } catch (error) {
      console.error('Start trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al iniciar viaje',
      };
    }
  },

  async completeTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/complete`);

      return {
        success: true,
        trip: response.data.trip,
        commission: response.data.commission,
      };
    } catch (error) {
      console.error('Complete trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al completar viaje',
      };
    }
  },

  async cancelTrip(tripId, reason) {
    try {
      const response = await api.post(`/trips/${tripId}/cancel`, {
        reason,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error) {
      console.error('Cancel trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al cancelar viaje',
      };
    }
  },

  async updateDriverLocation(tripId, location) {
    try {
      const response = await api.post(`/trips/${tripId}/location`, {
        latitude: location.latitude,
        longitude: location.longitude,
      });

      return {
        success: true,
      };
    } catch (error) {
      console.error('Update driver location error:', error);
      return {
        success: false,
        error: 'Error al actualizar ubicación',
      };
    }
  },

  async getTripHistory(userId) {
    const result = await apiRequest(
      () => api.get(`/trips/history/${userId}`),
      {
        trips: OFFLINE_DATA.tripHistory,
      },
      'Error al obtener historial de viajes'
    );

    if (result.success) {
      return {
        success: true,
        trips: result.data.trips,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },
};
