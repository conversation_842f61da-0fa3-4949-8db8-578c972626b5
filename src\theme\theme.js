import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// Colores personalizados de Maclaren
const maclarenColors = {
  primary: '#ff6b6b',
  primaryContainer: '#ffebee',
  secondary: '#2196F3',
  secondaryContainer: '#e3f2fd',
  tertiary: '#4CAF50',
  tertiaryContainer: '#e8f5e8',
  surface: '#ffffff',
  surfaceVariant: '#f5f5f5',
  background: '#f8f9fa',
  error: '#d32f2f',
  errorContainer: '#ffebee',
  onPrimary: '#ffffff',
  onPrimaryContainer: '#d32f2f',
  onSecondary: '#ffffff',
  onSecondaryContainer: '#1976d2',
  onTertiary: '#ffffff',
  onTertiaryContainer: '#2e7d32',
  onSurface: '#333333',
  onSurfaceVariant: '#666666',
  onBackground: '#333333',
  onError: '#ffffff',
  onErrorContainer: '#d32f2f',
  outline: '#dddddd',
  outlineVariant: '#eeeeee',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#333333',
  inverseOnSurface: '#ffffff',
  inversePrimary: '#ff8a80',
  elevation: {
    level0: 'transparent',
    level1: '#ffffff',
    level2: '#f8f9fa',
    level3: '#f5f5f5',
    level4: '#f0f0f0',
    level5: '#eeeeee',
  },
};

// Tema claro personalizado
export const maclarenLightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...maclarenColors,
  },
  fonts: {
    ...MD3LightTheme.fonts,
    displayLarge: {
      ...MD3LightTheme.fonts.displayLarge,
      fontWeight: 'bold',
    },
    displayMedium: {
      ...MD3LightTheme.fonts.displayMedium,
      fontWeight: 'bold',
    },
    displaySmall: {
      ...MD3LightTheme.fonts.displaySmall,
      fontWeight: 'bold',
    },
    headlineLarge: {
      ...MD3LightTheme.fonts.headlineLarge,
      fontWeight: 'bold',
    },
    headlineMedium: {
      ...MD3LightTheme.fonts.headlineMedium,
      fontWeight: 'bold',
    },
    headlineSmall: {
      ...MD3LightTheme.fonts.headlineSmall,
      fontWeight: '600',
    },
  },
  roundness: 12,
};

// Tema oscuro personalizado
export const maclarenDarkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#ff8a80',
    primaryContainer: '#d32f2f',
    secondary: '#64b5f6',
    secondaryContainer: '#1976d2',
    tertiary: '#81c784',
    tertiaryContainer: '#2e7d32',
    surface: '#1e1e1e',
    surfaceVariant: '#2d2d2d',
    background: '#121212',
    onPrimary: '#000000',
    onSecondary: '#000000',
    onTertiary: '#000000',
    onSurface: '#ffffff',
    onBackground: '#ffffff',
  },
  fonts: {
    ...MD3DarkTheme.fonts,
    displayLarge: {
      ...MD3DarkTheme.fonts.displayLarge,
      fontWeight: 'bold',
    },
    displayMedium: {
      ...MD3DarkTheme.fonts.displayMedium,
      fontWeight: 'bold',
    },
    displaySmall: {
      ...MD3DarkTheme.fonts.displaySmall,
      fontWeight: 'bold',
    },
    headlineLarge: {
      ...MD3DarkTheme.fonts.headlineLarge,
      fontWeight: 'bold',
    },
    headlineMedium: {
      ...MD3DarkTheme.fonts.headlineMedium,
      fontWeight: 'bold',
    },
    headlineSmall: {
      ...MD3DarkTheme.fonts.headlineSmall,
      fontWeight: '600',
    },
  },
  roundness: 12,
};

// Gradientes personalizados
export const gradients = {
  primary: ['#ff6b6b', '#ee5a24'],
  secondary: ['#2196F3', '#1976d2'],
  success: ['#4CAF50', '#45a049'],
  warning: ['#ff9800', '#f57c00'],
  error: ['#f44336', '#d32f2f'],
  dark: ['#1a1a2e', '#16213e', '#0f3460'],
  light: ['#ffffff', '#f8f9fa'],
};

// Sombras personalizadas
export const shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 8,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 12,
  },
};

// Espaciado consistente
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Tamaños de fuente
export const fontSizes = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 24,
  xxl: 32,
  xxxl: 48,
};
