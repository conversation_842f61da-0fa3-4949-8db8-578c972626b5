// Prueba simple de WebSocket
const io = require('socket.io-client');

console.log('🧪 Iniciando prueba simple de WebSocket...');

const socket = io('http://localhost:3000', {
  transports: ['polling', 'websocket'],
  timeout: 10000,
});

socket.on('connect', () => {
  console.log('✅ Conectado exitosamente:', socket.id);
  
  // Configurar sesión de usuario
  socket.emit('user-session', {
    userId: 'test-user-123',
    userType: 'passenger'
  });
  
  // Unirse a una sala de viaje de prueba
  const testTripId = 'test-trip-123';
  socket.emit('join-trip', testTripId);
  console.log('🏠 Unido a sala de viaje:', testTripId);
  
  // Simular solicitud de conductor después de 2 segundos
  setTimeout(() => {
    console.log('📤 Enviando solicitud de conductor...');
    socket.emit('driver-request', {
      tripId: testTripId,
      driverInfo: {
        id: 'test-driver-456',
        firstName: 'Carlos',
        lastName: 'Test',
        phone: '+507 6000-0000',
        rating: 4.8,
        vehicle: {
          brand: 'Toyota',
          model: 'Corolla',
          plate: 'TEST-123'
        }
      },
      timestamp: new Date().toISOString()
    });
  }, 2000);
});

socket.on('driver-request-received', (data) => {
  console.log('📥 ✅ Solicitud de conductor recibida correctamente:', data);
  
  // Simular respuesta del pasajero después de 1 segundo
  setTimeout(() => {
    console.log('📤 Enviando respuesta del pasajero (aceptado)...');
    socket.emit('passenger-response', {
      tripId: data.tripId,
      driverId: data.driverInfo.id,
      accepted: true,
      shouldOpenNavigation: true,
      pickupLocation: {
        latitude: 9.0765,
        longitude: -79.5340
      },
      timestamp: new Date().toISOString()
    });
  }, 1000);
});

socket.on('passenger-response', (data) => {
  console.log('📥 ✅ Respuesta del pasajero recibida correctamente:', data);
  console.log('🎉 ¡Prueba de WebSocket completada exitosamente!');
  
  // Cerrar conexión después de 1 segundo
  setTimeout(() => {
    socket.disconnect();
    process.exit(0);
  }, 1000);
});

socket.on('connect_error', (error) => {
  console.error('❌ Error de conexión:', error.message);
  console.error('❌ Detalles:', error);
  process.exit(1);
});

socket.on('disconnect', (reason) => {
  console.log('❌ Desconectado:', reason);
});

// Timeout de seguridad
setTimeout(() => {
  console.log('⏰ Timeout - Cerrando prueba...');
  socket.disconnect();
  process.exit(1);
}, 15000);
