// Script de prueba para WebSocket
const io = require('socket.io-client');

console.log('🧪 Iniciando prueba de WebSocket...');

const socket = io('http://localhost:3000', {
  transports: ['websocket', 'polling'],
  timeout: 10000,
});

socket.on('connect', () => {
  console.log('✅ Conectado al servidor WebSocket:', socket.id);
  
  // Simular sesión de usuario
  socket.emit('user-session', {
    userId: 'test-user-123',
    userType: 'passenger'
  });
  
  // Unirse a una sala de viaje de prueba
  const testTripId = 'test-trip-123';
  socket.emit('join-trip', testTripId);
  console.log('🏠 Unido a sala de viaje:', testTripId);
  
  // Simular solicitud de conductor
  setTimeout(() => {
    console.log('📤 Enviando solicitud de conductor...');
    socket.emit('driver-request', {
      tripId: testTripId,
      driverInfo: {
        id: 'test-driver-456',
        firstName: 'Carlos',
        lastName: 'Test',
        phone: '+507 6000-0000',
        rating: 4.8,
        vehicle: {
          brand: 'Toyota',
          model: 'Corolla',
          plate: 'TEST-123'
        }
      },
      timestamp: new Date().toISOString()
    });
  }, 2000);
});

socket.on('driver-request-received', (data) => {
  console.log('📥 Solicitud de conductor recibida:', data);
});

socket.on('passenger-response', (data) => {
  console.log('📥 Respuesta del pasajero recibida:', data);
});

socket.on('connect_error', (error) => {
  console.error('❌ Error de conexión:', error);
});

socket.on('disconnect', () => {
  console.log('❌ Desconectado del servidor');
});

// Mantener el script corriendo por 30 segundos
setTimeout(() => {
  console.log('🏁 Finalizando prueba...');
  socket.disconnect();
  process.exit(0);
}, 30000);
